<?php
/**
 * Test new WebP deletion system
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Testing New WebP Deletion System ===\n\n";

// 1. Check if WebP module is enabled
$enabled_modules = get_option('redco_optimizer_enabled_modules', array());
$webp_enabled = in_array('smart-webp-conversion', $enabled_modules);

echo "1. Module Status:\n";
echo "   Enabled modules: " . implode(', ', $enabled_modules) . "\n";
echo "   WebP enabled: " . ($webp_enabled ? '✅ YES' : '❌ NO') . "\n\n";

if (!$webp_enabled) {
    echo "❌ WebP module is not enabled. Enabling it now...\n";
    
    // Enable the WebP module
    if (!in_array('smart-webp-conversion', $enabled_modules)) {
        $enabled_modules[] = 'smart-webp-conversion';
        update_option('redco_optimizer_enabled_modules', $enabled_modules);
        echo "✅ WebP module enabled successfully\n\n";
    }
}

// 2. Check if deletion handler class exists
echo "2. Deletion Handler Class:\n";
if (class_exists('Redco_WebP_Deletion_Handler')) {
    echo "   ✅ Redco_WebP_Deletion_Handler class exists\n";
} else {
    echo "   ❌ Redco_WebP_Deletion_Handler class not found\n";
    
    // Try to load it manually
    $handler_file = 'wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/webp-deletion-handler.php';
    if (file_exists($handler_file)) {
        echo "   📁 Handler file exists, loading manually...\n";
        require_once($handler_file);
        
        if (class_exists('Redco_WebP_Deletion_Handler')) {
            echo "   ✅ Class loaded successfully after manual include\n";
        } else {
            echo "   ❌ Class still not found after manual include\n";
        }
    } else {
        echo "   ❌ Handler file not found: {$handler_file}\n";
    }
}

// 3. Check hook registration
global $wp_filter;

echo "\n3. Hook Registration Check:\n";

$hooks_to_check = [
    'delete_attachment',
    'wp_delete_post',
    'wp_delete_attachment_files'
];

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "   ✅ {$hook}: " . count($wp_filter[$hook]->callbacks) . " callbacks\n";
        
        // Check for WebP deletion handler callbacks
        $webp_found = false;
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'Redco_WebP_Deletion_Handler') {
                    echo "      - WebP Handler: {$callback['function'][1]} (priority: {$priority})\n";
                    $webp_found = true;
                }
            }
        }
        
        if (!$webp_found) {
            echo "      ⚠️ No WebP deletion handler callbacks found\n";
        }
    } else {
        echo "   ❌ {$hook}: No callbacks registered\n";
    }
}

// 4. Manual instantiation test
echo "\n4. Manual Instantiation Test:\n";

try {
    if (class_exists('Redco_WebP_Deletion_Handler')) {
        $handler = new Redco_WebP_Deletion_Handler();
        echo "   ✅ Handler instantiated successfully\n";
        
        // Check if hooks are now registered
        if (isset($wp_filter['delete_attachment'])) {
            $webp_found = false;
            foreach ($wp_filter['delete_attachment']->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        is_object($callback['function'][0]) && 
                        get_class($callback['function'][0]) === 'Redco_WebP_Deletion_Handler') {
                        echo "   ✅ WebP deletion hooks now registered after manual instantiation\n";
                        $webp_found = true;
                        break 2;
                    }
                }
            }
            
            if (!$webp_found) {
                echo "   ❌ WebP deletion hooks still not found after manual instantiation\n";
            }
        }
        
    } else {
        echo "   ❌ Cannot instantiate - class not found\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Failed to instantiate handler: " . $e->getMessage() . "\n";
}

// 5. Test with a real attachment
echo "\n5. Real Attachment Test:\n";

// Find a WebP converted attachment
global $wpdb;
$webp_attachment = $wpdb->get_row("
    SELECT p.ID, p.post_title, pm.meta_value
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
    WHERE p.post_type = 'attachment'
    AND pm.meta_key = '_webp_conversion_data'
    AND (pm.meta_value LIKE '%\"converted\":true%' OR pm.meta_value LIKE '%s:9:\"converted\";b:1%')
    LIMIT 1
");

if ($webp_attachment) {
    echo "   ✅ Found WebP converted attachment: ID {$webp_attachment->ID} - {$webp_attachment->post_title}\n";
    
    $conversion_data = maybe_unserialize($webp_attachment->meta_value);
    if ($conversion_data && isset($conversion_data['webp_path'])) {
        echo "   ✅ Conversion data valid\n";
        echo "   📁 WebP path: " . $conversion_data['webp_path'] . "\n";
        echo "   📁 WebP exists: " . (file_exists($conversion_data['webp_path']) ? '✅ YES' : '❌ NO') . "\n";
        
        // Test if we can manually call the deletion handler
        if (class_exists('Redco_WebP_Deletion_Handler')) {
            echo "   🧪 Testing manual deletion handler call...\n";
            
            // Don't actually delete, just test the method exists
            $handler = new Redco_WebP_Deletion_Handler();
            if (method_exists($handler, 'handle_attachment_deletion')) {
                echo "   ✅ handle_attachment_deletion method exists\n";
            } else {
                echo "   ❌ handle_attachment_deletion method not found\n";
            }
        }
    } else {
        echo "   ❌ Invalid conversion data\n";
    }
} else {
    echo "   ❌ No WebP converted attachments found\n";
}

echo "\n=== Test Complete ===\n";
