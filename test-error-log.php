<?php
/**
 * Test error logging
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Testing Error Logging ===\n\n";

// Test error_log
error_log("🔥 TEST: This is a test error log message");

// Check if WP_DEBUG is enabled
echo "WP_DEBUG: " . (defined('WP_DEBUG') && WP_DEBUG ? 'ENABLED' : 'DISABLED') . "\n";
echo "WP_DEBUG_LOG: " . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'ENABLED' : 'DISABLED') . "\n";

// Check error log file location
$error_log_file = ini_get('error_log');
echo "PHP error_log file: " . ($error_log_file ? $error_log_file : 'NOT SET') . "\n";

// Check WordPress debug log
$wp_debug_log = WP_CONTENT_DIR . '/debug.log';
echo "WordPress debug.log: " . $wp_debug_log . "\n";
echo "Debug log exists: " . (file_exists($wp_debug_log) ? 'YES' : 'NO') . "\n";
echo "Debug log writable: " . (is_writable($wp_debug_log) ? 'YES' : 'NO') . "\n";

if (file_exists($wp_debug_log)) {
    echo "Debug log size: " . filesize($wp_debug_log) . " bytes\n";
    echo "Debug log modified: " . date('Y-m-d H:i:s', filemtime($wp_debug_log)) . "\n";
}

echo "\n=== Test Complete ===\n";
