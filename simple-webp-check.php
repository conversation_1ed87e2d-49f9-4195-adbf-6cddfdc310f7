<?php
/**
 * Simple WebP metadata check
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Simple WebP Metadata Check ===\n\n";

global $wpdb;

// Direct database query for WebP conversion data
$results = $wpdb->get_results("
    SELECT pm.post_id, pm.meta_value, p.post_title
    FROM {$wpdb->postmeta} pm
    LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
    WHERE pm.meta_key = '_webp_conversion_data'
    LIMIT 3
", ARRAY_A);

echo "Found " . count($results) . " records with _webp_conversion_data\n\n";

foreach ($results as $result) {
    echo "==========================================\n";
    echo "Post ID: " . $result['post_id'] . "\n";
    echo "Title: " . ($result['post_title'] ?: 'No title') . "\n";
    echo "Raw metadata (first 500 chars):\n";
    echo substr($result['meta_value'], 0, 500) . "\n\n";
    
    // Try to unserialize
    $data = maybe_unserialize($result['meta_value']);
    if (is_array($data)) {
        echo "Parsed data keys: " . implode(', ', array_keys($data)) . "\n";
        
        if (isset($data['webp_path'])) {
            echo "WebP path: " . $data['webp_path'] . "\n";
            echo "WebP exists: " . (file_exists($data['webp_path']) ? 'YES' : 'NO') . "\n";
        }
        
        if (isset($data['file_path'])) {
            echo "Original path: " . $data['file_path'] . "\n";
            echo "Original exists: " . (file_exists($data['file_path']) ? 'YES' : 'NO') . "\n";
        }
        
        if (isset($data['converted'])) {
            echo "Converted: " . ($data['converted'] ? 'YES' : 'NO') . "\n";
        }
        
        if (isset($data['original_preserved'])) {
            echo "Architecture: " . ($data['original_preserved'] ? 'NEW (preserved)' : 'LEGACY (modified)') . "\n";
        }
    } else {
        echo "Failed to parse metadata\n";
    }
    echo "\n";
}

echo "=== Check Complete ===\n";
