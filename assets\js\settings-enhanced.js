/**
 * Clean Settings Page JavaScript
 * Minimal, essential functionality only
 * Redco Optimizer Plugin
 */

(function($) {
    'use strict';

    // Clean settings page object
    const RedcoSettingsClean = {

        // Initialize essential features only
        init: function() {
            this.initFormValidation();
            this.initAutoSave();
        },

        // Comprehensive auto-save for all form field types
        initAutoSave: function() {
            let saveTimeout;
            const SAVE_DELAY = 1000; // 1 second delay

            // COMPREHENSIVE AUTO-SAVE: Bind to ALL form field types
            $(document).on('change input', '.redco-settings-form input, .redco-settings-form select, .redco-settings-form textarea', function() {
                const $element = $(this);
                const elementType = $element.prop('tagName').toLowerCase();
                const inputType = $element.attr('type');

                // Skip if element doesn't have proper name attribute
                if (!$element.attr('name')) {
                    return;
                }

                // Extract setting group and name from name attribute
                let settingGroup, settingName, value;

                // Handle different name formats
                const nameAttr = $element.attr('name');
                if (nameAttr.includes('[') && nameAttr.includes(']')) {
                    // Format: group[setting] or group[setting][sub]
                    const matches = nameAttr.match(/^([^[]+)\[([^\]]+)\](?:\[([^\]]+)\])?$/);
                    if (matches) {
                        settingGroup = matches[1];
                        settingName = matches[2];

                        // Handle sub-settings (like arrays)
                        if (matches[3]) {
                            settingName = matches[2] + '[' + matches[3] + ']';
                        }
                    }
                } else if ($element.data('setting-group') && $element.data('setting-name')) {
                    // Use data attributes if available
                    settingGroup = $element.data('setting-group');
                    settingName = $element.data('setting-name');
                } else {
                    // Skip if we can't determine the setting structure
                    return;
                }

                // Get value based on element type
                if (inputType === 'checkbox') {
                    value = $element.is(':checked') ? 1 : 0;
                } else if (inputType === 'radio') {
                    // Only save if this radio button is checked
                    if (!$element.is(':checked')) {
                        return;
                    }
                    value = $element.val();
                } else if (elementType === 'select') {
                    value = $element.val();
                } else if (elementType === 'textarea') {
                    value = $element.val();
                } else {
                    // text, number, email, url, etc.
                    value = $element.val();
                }

                // Clear existing timeout
                clearTimeout(saveTimeout);

                // Add visual feedback
                $element.closest('.setting-item').addClass('saving');

                // Save after delay
                saveTimeout = setTimeout(function() {
                    RedcoSettingsClean.autoSaveSetting(settingGroup, settingName, value, $element);
                }, SAVE_DELAY);
            });
        },

        // Auto-save function
        autoSaveSetting: function(group, name, value, $element) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_auto_save_setting',
                    nonce: redco_settings ? redco_settings.nonce : '',
                    group: group,
                    name: name,
                    value: value
                },
                success: function(response) {
                    // Remove saving indicator
                    $element.closest('.setting-item').removeClass('saving');

                    if (response.success) {
                        // Use global toast notification system only
                        if (typeof showToast === 'function') {
                            showToast(response.data.message || 'Setting saved successfully', 'success', 2000);
                        }
                    } else {
                        // Use global toast notification system for errors
                        const errorMessage = response.data && response.data.message ? response.data.message : 'Failed to save setting';
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error', 4000);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    // Remove saving indicator
                    $element.closest('.setting-item').removeClass('saving');

                    // Use global toast notification system for AJAX errors
                    if (typeof showToast === 'function') {
                        showToast('Network error: Unable to save setting', 'error', 4000);
                    }
                }
            });
        },

        // Basic form validation
        initFormValidation: function() {
            $('.redco-settings-form').on('submit', function(e) {
                let isValid = true;

                // Simple required field validation
                $(this).find('[required]').each(function() {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).css('border-color', '#e74c3c');
                    } else {
                        $(this).css('border-color', '');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    alert('Please fill in all required fields');
                }
            });
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Only initialize on settings pages
        if ($('.redco-optimizer-settings').length) {
            RedcoSettingsClean.init();
        }
    });

    // Make it globally available
    window.RedcoSettingsClean = RedcoSettingsClean;

})(jQuery);
