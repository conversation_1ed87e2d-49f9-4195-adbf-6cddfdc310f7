<?php
/**
 * Check WebP conversion metadata records
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== WebP Conversion Metadata Records ===\n\n";

// Get all WebP converted attachments
global $wpdb;
$webp_attachments = $wpdb->get_results("
    SELECT p.ID, p.post_title, p.post_mime_type, pm.meta_value
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
    WHERE p.post_type = 'attachment'
    AND pm.meta_key = '_webp_conversion_data'
    AND (pm.meta_value LIKE '%\"converted\":true%' OR pm.meta_value LIKE '%s:9:\"converted\";b:1%')
    ORDER BY p.ID DESC
    LIMIT 5
");

if (empty($webp_attachments)) {
    echo "❌ No WebP converted attachments found\n";
    exit;
}

echo "✅ Found " . count($webp_attachments) . " WebP converted attachments:\n\n";

foreach ($webp_attachments as $attachment) {
    echo "==========================================\n";
    echo "📄 Attachment ID: {$attachment->ID}\n";
    echo "📄 Title: {$attachment->post_title}\n";
    echo "📄 MIME Type: {$attachment->post_mime_type}\n";
    echo "📄 WordPress File Path: " . get_attached_file($attachment->ID) . "\n";
    
    // Parse conversion data
    $conversion_data = maybe_unserialize($attachment->meta_value);
    
    echo "\n🔍 CONVERSION METADATA:\n";
    echo "Raw metadata: " . substr($attachment->meta_value, 0, 200) . "...\n\n";
    
    if (is_array($conversion_data)) {
        echo "📊 Parsed Conversion Data:\n";
        
        // Check architecture type
        $is_new_architecture = isset($conversion_data['original_preserved']) && $conversion_data['original_preserved'];
        echo "   Architecture: " . ($is_new_architecture ? 'NEW (original preserved)' : 'LEGACY (original modified)') . "\n";
        
        // Main file paths
        if (isset($conversion_data['file_path'])) {
            echo "   Original file_path: " . $conversion_data['file_path'] . "\n";
            echo "   Original exists: " . (file_exists($conversion_data['file_path']) ? '✅ YES' : '❌ NO') . "\n";
        }
        
        if (isset($conversion_data['webp_path'])) {
            echo "   WebP file_path: " . $conversion_data['webp_path'] . "\n";
            echo "   WebP exists: " . (file_exists($conversion_data['webp_path']) ? '✅ YES' : '❌ NO') . "\n";
        }
        
        // File sizes
        if (isset($conversion_data['original_size'])) {
            echo "   Original size: " . number_format($conversion_data['original_size']) . " bytes\n";
        }
        
        if (isset($conversion_data['webp_size'])) {
            echo "   WebP size: " . number_format($conversion_data['webp_size']) . " bytes\n";
        }
        
        // Conversion status
        if (isset($conversion_data['converted'])) {
            echo "   Converted: " . ($conversion_data['converted'] ? '✅ YES' : '❌ NO') . "\n";
        }
        
        // Thumbnail data
        if (isset($conversion_data['webp_sizes']) && is_array($conversion_data['webp_sizes'])) {
            echo "   WebP thumbnails:\n";
            foreach ($conversion_data['webp_sizes'] as $size_name => $webp_path) {
                echo "      - {$size_name}: {$webp_path} " . (file_exists($webp_path) ? '✅' : '❌') . "\n";
            }
        }
        
        if (isset($conversion_data['original_sizes']) && is_array($conversion_data['original_sizes'])) {
            echo "   Original thumbnails:\n";
            foreach ($conversion_data['original_sizes'] as $size_name => $original_path) {
                echo "      - {$size_name}: {$original_path} " . (file_exists($original_path) ? '✅' : '❌') . "\n";
            }
        }
        
        // All metadata keys
        echo "\n   All metadata keys:\n";
        foreach ($conversion_data as $key => $value) {
            if (is_array($value)) {
                echo "      - {$key}: [array with " . count($value) . " items]\n";
            } elseif (is_string($value) && strlen($value) > 50) {
                echo "      - {$key}: " . substr($value, 0, 50) . "...\n";
            } else {
                echo "      - {$key}: " . var_export($value, true) . "\n";
            }
        }
        
    } else {
        echo "❌ Failed to parse conversion data\n";
    }
    
    // Check WordPress attachment metadata
    echo "\n📊 WordPress Attachment Metadata:\n";
    $wp_metadata = wp_get_attachment_metadata($attachment->ID);
    if ($wp_metadata) {
        echo "   File: " . (isset($wp_metadata['file']) ? $wp_metadata['file'] : 'NOT SET') . "\n";
        echo "   Width: " . (isset($wp_metadata['width']) ? $wp_metadata['width'] : 'NOT SET') . "\n";
        echo "   Height: " . (isset($wp_metadata['height']) ? $wp_metadata['height'] : 'NOT SET') . "\n";
        
        if (isset($wp_metadata['sizes']) && is_array($wp_metadata['sizes'])) {
            echo "   WordPress thumbnails:\n";
            foreach ($wp_metadata['sizes'] as $size_name => $size_data) {
                if (isset($size_data['file'])) {
                    $upload_dir = wp_upload_dir();
                    $thumbnail_path = dirname($upload_dir['basedir'] . '/' . $wp_metadata['file']) . '/' . $size_data['file'];
                    echo "      - {$size_name}: {$size_data['file']} " . (file_exists($thumbnail_path) ? '✅' : '❌') . "\n";
                }
            }
        }
    } else {
        echo "   ❌ No WordPress metadata found\n";
    }
    
    echo "\n";
}

echo "=== Metadata Check Complete ===\n";
