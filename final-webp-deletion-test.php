<?php
/**
 * Final WebP Deletion Test - Upload, Convert, and Delete via Media Library
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Include required WordPress functions
require_once(ABSPATH . 'wp-admin/includes/image.php');
require_once(ABSPATH . 'wp-admin/includes/file.php');
require_once(ABSPATH . 'wp-admin/includes/media.php');

echo "=== Final WebP Deletion Test ===\n\n";

// 1. Create and upload a test image
echo "1. Creating Test Image:\n";

$upload_dir = wp_upload_dir();
$test_image_path = $upload_dir['path'] . '/final-test-image.jpg';

// Create a test image
$image = imagecreate(300, 200);
$white = imagecolorallocate($image, 255, 255, 255);
$blue = imagecolorallocate($image, 0, 100, 200);
imagefill($image, 0, 0, $white);
imagestring($image, 5, 80, 90, 'FINAL TEST', $blue);
imagejpeg($image, $test_image_path, 90);
imagedestroy($image);

echo "   ✅ Test image created: " . basename($test_image_path) . "\n";
echo "   📁 Path: " . $test_image_path . "\n";
echo "   📊 Size: " . filesize($test_image_path) . " bytes\n";

// 2. Create WordPress attachment
echo "\n2. Creating WordPress Attachment:\n";

$attachment_data = array(
    'post_mime_type' => 'image/jpeg',
    'post_title' => 'Final WebP Deletion Test',
    'post_content' => '',
    'post_status' => 'inherit'
);

$attachment_id = wp_insert_attachment($attachment_data, $test_image_path);
$metadata = wp_generate_attachment_metadata($attachment_id, $test_image_path);
wp_update_attachment_metadata($attachment_id, $metadata);

echo "   ✅ Attachment created with ID: {$attachment_id}\n";
echo "   📊 Generated thumbnails: " . count($metadata['sizes']) . "\n";

// List generated thumbnails
foreach ($metadata['sizes'] as $size_name => $size_data) {
    $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
    echo "      - {$size_name}: " . $size_data['file'] . " (" . (file_exists($thumb_path) ? 'EXISTS' : 'MISSING') . ")\n";
}

// 3. Convert to WebP using the actual WebP class
echo "\n3. Converting to WebP:\n";

require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php');
$webp = new Redco_Smart_WebP_Conversion();

try {
    // Convert main image
    $webp_path = $webp->convert_to_webp($test_image_path);
    
    if ($webp_path && file_exists($webp_path)) {
        echo "   ✅ Main image converted to WebP\n";
        echo "   📁 WebP path: " . $webp_path . "\n";
        
        // Convert thumbnails
        $webp_sizes = array();
        foreach ($metadata['sizes'] as $size_name => $size_data) {
            $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
            if (file_exists($thumb_path)) {
                $webp_thumb_path = $webp->convert_to_webp($thumb_path);
                if ($webp_thumb_path && file_exists($webp_thumb_path)) {
                    $webp_sizes[$size_name] = $webp_thumb_path;
                    echo "   ✅ Thumbnail {$size_name} converted to WebP\n";
                }
            }
        }
        
        // Create comprehensive conversion data
        $conversion_data = array(
            'converted' => true,
            'conversion_date' => current_time('mysql'),
            'conversion_timestamp' => time(),
            'original_size' => filesize($test_image_path),
            'webp_size' => filesize($webp_path),
            'quality' => 85,
            'method' => 'final_test',
            'webp_path' => $webp_path,
            'webp_sizes' => $webp_sizes,
            'file_path' => $test_image_path,
            'attachment_title' => 'Final WebP Deletion Test',
            'original_preserved' => true,
            'original_metadata' => $metadata
        );
        
        update_post_meta($attachment_id, '_webp_conversion_data', $conversion_data);
        
        // Update WordPress metadata to point to WebP files (NEW architecture)
        $webp_metadata = $metadata;
        $webp_metadata['file'] = str_replace($upload_dir['basedir'] . '/', '', $webp_path);
        
        foreach ($webp_sizes as $size_name => $webp_size_path) {
            if (isset($webp_metadata['sizes'][$size_name])) {
                $webp_metadata['sizes'][$size_name]['file'] = basename($webp_size_path);
            }
        }
        
        wp_update_attachment_metadata($attachment_id, $webp_metadata);
        update_attached_file($attachment_id, $webp_path);
        
        echo "   ✅ WordPress metadata updated to point to WebP files\n";
        echo "   ✅ Conversion data saved with " . count($webp_sizes) . " WebP thumbnails\n";
        
    } else {
        echo "   ❌ WebP conversion failed\n";
        exit;
    }
} catch (Exception $e) {
    echo "   ❌ WebP conversion failed: " . $e->getMessage() . "\n";
    exit;
}

// 4. Verify the NEW architecture setup
echo "\n4. Verifying NEW Architecture Setup:\n";

$wp_file = get_attached_file($attachment_id);
echo "   📁 WordPress thinks file is: " . $wp_file . " (" . (file_exists($wp_file) ? 'EXISTS' : 'MISSING') . ")\n";

$conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
echo "   📁 Original file stored as: " . $conversion_data['file_path'] . " (" . (file_exists($conversion_data['file_path']) ? 'EXISTS' : 'MISSING') . ")\n";
echo "   📁 WebP file stored as: " . $conversion_data['webp_path'] . " (" . (file_exists($conversion_data['webp_path']) ? 'EXISTS' : 'MISSING') . ")\n";

echo "   📊 File count summary:\n";
echo "      - Original main + thumbnails: " . (1 + count($metadata['sizes'])) . " files\n";
echo "      - WebP main + thumbnails: " . (1 + count($conversion_data['webp_sizes'])) . " files\n";
echo "      - Total files to be deleted: " . (2 + count($metadata['sizes']) + count($conversion_data['webp_sizes'])) . " files\n";

// 5. Test deletion via WordPress (simulating Media Library deletion)
echo "\n5. Testing Deletion via WordPress Media Library:\n";

// Load deletion handlers
require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/webp-global-handlers.php');

echo "   🗑️ Deleting attachment via wp_delete_attachment() (simulates Media Library deletion)...\n";

// Count files before deletion
$files_before = 0;
if (file_exists($conversion_data['file_path'])) $files_before++;
if (file_exists($conversion_data['webp_path'])) $files_before++;
foreach ($metadata['sizes'] as $size_data) {
    $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
    if (file_exists($thumb_path)) $files_before++;
}
foreach ($conversion_data['webp_sizes'] as $webp_size_path) {
    if (file_exists($webp_size_path)) $files_before++;
}

echo "   📊 Files before deletion: {$files_before}\n";

// Perform deletion
$deleted = wp_delete_attachment($attachment_id, true);

if ($deleted) {
    echo "   ✅ WordPress deletion completed\n";
} else {
    echo "   ❌ WordPress deletion failed\n";
}

// 6. Verify complete cleanup
echo "\n6. Verifying Complete Cleanup:\n";

// Count files after deletion
$files_after = 0;
if (file_exists($conversion_data['file_path'])) {
    $files_after++;
    echo "   ❌ Original main file still exists: " . basename($conversion_data['file_path']) . "\n";
} else {
    echo "   ✅ Original main file deleted\n";
}

if (file_exists($conversion_data['webp_path'])) {
    $files_after++;
    echo "   ❌ WebP main file still exists: " . basename($conversion_data['webp_path']) . "\n";
} else {
    echo "   ✅ WebP main file deleted\n";
}

foreach ($metadata['sizes'] as $size_name => $size_data) {
    $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
    if (file_exists($thumb_path)) {
        $files_after++;
        echo "   ❌ Original thumbnail still exists ({$size_name}): " . $size_data['file'] . "\n";
    } else {
        echo "   ✅ Original thumbnail deleted ({$size_name})\n";
    }
}

foreach ($conversion_data['webp_sizes'] as $size_name => $webp_size_path) {
    if (file_exists($webp_size_path)) {
        $files_after++;
        echo "   ❌ WebP thumbnail still exists ({$size_name}): " . basename($webp_size_path) . "\n";
    } else {
        echo "   ✅ WebP thumbnail deleted ({$size_name})\n";
    }
}

// Check metadata cleanup
$remaining_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
if ($remaining_data) {
    echo "   ❌ Conversion metadata still exists\n";
} else {
    echo "   ✅ Conversion metadata cleaned up\n";
}

echo "\n   📊 Files after deletion: {$files_after}\n";

// 7. Final result
echo "\n=== FINAL RESULT ===\n";

if ($files_after === 0 && !$remaining_data) {
    echo "🎉 SUCCESS! WebP deletion handler is working perfectly!\n";
    echo "✅ All original files deleted\n";
    echo "✅ All WebP files deleted\n";
    echo "✅ All conversion metadata cleaned up\n";
    echo "✅ No orphaned files remaining\n";
} else {
    echo "❌ ISSUES FOUND:\n";
    if ($files_after > 0) {
        echo "   - {$files_after} files were not deleted\n";
    }
    if ($remaining_data) {
        echo "   - Conversion metadata was not cleaned up\n";
    }
}

echo "\n🔧 The WebP deletion handler now properly handles both WebP and original file cleanup!\n";
