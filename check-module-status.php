<?php
/**
 * Check WebP module status and hook registration
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== WebP Module Status Check ===\n\n";

// 1. Check if module is enabled
$enabled_modules = get_option('redco_optimizer_enabled_modules', array());
$webp_enabled = in_array('smart-webp-conversion', $enabled_modules);

echo "1. Module Status:\n";
echo "   Enabled modules: " . implode(', ', $enabled_modules) . "\n";
echo "   WebP enabled: " . ($webp_enabled ? '✅ YES' : '❌ NO') . "\n\n";

// 2. Check global instance
global $redco_webp_instance;
echo "2. Global Instance:\n";
echo "   Global instance exists: " . ($redco_webp_instance ? '✅ YES' : '❌ NO') . "\n";
if ($redco_webp_instance) {
    echo "   Instance class: " . get_class($redco_webp_instance) . "\n";
}
echo "\n";

// 3. Check if class exists
echo "3. Class Status:\n";
echo "   Class exists: " . (class_exists('Redco_Smart_WebP_Conversion') ? '✅ YES' : '❌ NO') . "\n\n";

// 4. Check hook registration
global $wp_filter;
echo "4. Hook Registration:\n";

$hooks_to_check = [
    'wp_delete_post',
    'deleted_post',
    'before_delete_post',
    'delete_attachment'
];

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "   ✅ {$hook}: " . count($wp_filter[$hook]->callbacks) . " callbacks\n";
        
        // Check for WebP callbacks
        $webp_found = false;
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function'])) {
                    if (is_object($callback['function'][0])) {
                        $class_name = get_class($callback['function'][0]);
                        if ($class_name === 'Redco_Smart_WebP_Conversion') {
                            echo "      - WebP: {$callback['function'][1]} (priority: {$priority})\n";
                            $webp_found = true;
                        }
                    }
                } elseif (is_callable($callback['function'])) {
                    if (strpos(serialize($callback['function']), 'IMMEDIATE_TEST') !== false) {
                        echo "      - Test hook found (priority: {$priority})\n";
                    }
                }
            }
        }
        
        if (!$webp_found) {
            echo "      ⚠️ No WebP callbacks found\n";
        }
    } else {
        echo "   ❌ {$hook}: No callbacks registered\n";
    }
}

echo "\n5. Manual Instantiation Test:\n";

// Try to manually create instance
if (!$redco_webp_instance) {
    try {
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php');
        }
        
        $test_instance = new Redco_Smart_WebP_Conversion();
        echo "   ✅ Manual instance created successfully\n";
        
        // Check if hooks are now registered
        if (isset($wp_filter['wp_delete_post'])) {
            $webp_found = false;
            foreach ($wp_filter['wp_delete_post']->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        is_object($callback['function'][0]) && 
                        get_class($callback['function'][0]) === 'Redco_Smart_WebP_Conversion') {
                        echo "   ✅ WebP hooks now registered after manual instantiation\n";
                        $webp_found = true;
                        break 2;
                    }
                }
            }
            
            if (!$webp_found) {
                echo "   ❌ WebP hooks still not found after manual instantiation\n";
            }
        }
        
    } catch (Exception $e) {
        echo "   ❌ Failed to create instance: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ✅ Global instance already exists\n";
}

echo "\n=== Status Check Complete ===\n";
