<?php
/**
 * Check deletion logs to see what's happening
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Checking WebP Deletion Logs ===\n\n";

// Check WordPress debug log
$debug_log_file = WP_CONTENT_DIR . '/debug.log';

if (file_exists($debug_log_file)) {
    echo "✅ Debug log found: " . $debug_log_file . "\n";
    
    // Read the last 50 lines of the log
    $log_content = file_get_contents($debug_log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -50);
    
    // Filter for WebP deletion logs
    $webp_logs = array();
    foreach ($recent_lines as $line) {
        if (strpos($line, 'WEBP CLEANUP') !== false || strpos($line, 'WEBP DELETION') !== false) {
            $webp_logs[] = $line;
        }
    }
    
    if (!empty($webp_logs)) {
        echo "\n📝 Recent WebP deletion logs:\n";
        foreach ($webp_logs as $log) {
            echo "   " . $log . "\n";
        }
    } else {
        echo "\n⚠️ No recent WebP deletion logs found\n";
        echo "This might mean:\n";
        echo "1. WP_DEBUG is not enabled\n";
        echo "2. Deletion hooks are not being triggered\n";
        echo "3. Logs are being written elsewhere\n";
    }
    
    // Check if WP_DEBUG is enabled
    if (defined('WP_DEBUG') && WP_DEBUG) {
        echo "\n✅ WP_DEBUG is enabled\n";
    } else {
        echo "\n❌ WP_DEBUG is not enabled - no debug logs will be written\n";
    }
    
} else {
    echo "❌ Debug log not found at: " . $debug_log_file . "\n";
    echo "This might mean:\n";
    echo "1. WP_DEBUG_LOG is not enabled\n";
    echo "2. No errors have been logged yet\n";
    echo "3. Log file is in a different location\n";
}

// Check WordPress configuration
echo "\n=== WordPress Debug Configuration ===\n";
echo "WP_DEBUG: " . (defined('WP_DEBUG') && WP_DEBUG ? 'TRUE' : 'FALSE') . "\n";
echo "WP_DEBUG_LOG: " . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'TRUE' : 'FALSE') . "\n";
echo "WP_DEBUG_DISPLAY: " . (defined('WP_DEBUG_DISPLAY') && WP_DEBUG_DISPLAY ? 'TRUE' : 'FALSE') . "\n";

echo "\n=== WebP Module Status ===\n";
$webp_enabled = redco_is_module_enabled('smart-webp-conversion');
echo "WebP Module: " . ($webp_enabled ? '✅ ENABLED' : '❌ DISABLED') . "\n";

// Test if deletion hooks are registered
echo "\n=== Hook Registration Check ===\n";

global $wp_filter;

$hooks_to_check = [
    'before_delete_post',
    'delete_attachment', 
    'wp_delete_attachment_files'
];

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "✅ {$hook}: " . count($wp_filter[$hook]->callbacks) . " callbacks registered\n";
        
        // Check for WebP deletion handler callbacks
        $webp_found = false;
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_string($callback['function']) && strpos($callback['function'], 'redco_webp_') === 0) {
                    echo "   - WebP Handler: {$callback['function']} (priority: {$priority})\n";
                    $webp_found = true;
                }
            }
        }
        
        if (!$webp_found) {
            echo "   ⚠️ No WebP deletion handler callbacks found\n";
        }
    } else {
        echo "❌ {$hook}: No callbacks registered\n";
    }
}

echo "\n=== Recommendations ===\n";
if (!defined('WP_DEBUG') || !WP_DEBUG) {
    echo "🔧 Enable WP_DEBUG in wp-config.php to see deletion logs\n";
}
if (!defined('WP_DEBUG_LOG') || !WP_DEBUG_LOG) {
    echo "🔧 Enable WP_DEBUG_LOG in wp-config.php to write logs to file\n";
}
if (!$webp_enabled) {
    echo "🔧 Enable WebP module to register deletion hooks\n";
}
