<?php
/**
 * Test manual WebP deletion
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Testing Manual WebP Deletion ===\n\n";

// Find a WebP converted attachment
global $wpdb;
$webp_attachment = $wpdb->get_row("
    SELECT p.ID, p.post_title, pm.meta_value
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
    WHERE p.post_type = 'attachment'
    AND pm.meta_key = '_webp_conversion_data'
    AND (pm.meta_value LIKE '%\"converted\":true%' OR pm.meta_value LIKE '%s:9:\"converted\";b:1%')
    LIMIT 1
");

if (!$webp_attachment) {
    echo "❌ No WebP converted attachments found\n";
    exit;
}

echo "✅ Found WebP converted attachment: ID {$webp_attachment->ID} - {$webp_attachment->post_title}\n";

$conversion_data = maybe_unserialize($webp_attachment->meta_value);
if (!$conversion_data || !isset($conversion_data['webp_path'])) {
    echo "❌ Invalid conversion data\n";
    exit;
}

echo "✅ Conversion data valid\n";
echo "📁 WebP path: " . $conversion_data['webp_path'] . "\n";
echo "📁 WebP exists: " . (file_exists($conversion_data['webp_path']) ? '✅ YES' : '❌ NO') . "\n";

// Check if original file exists
$original_file = get_attached_file($webp_attachment->ID);
echo "📁 Original path: " . $original_file . "\n";
echo "📁 Original exists: " . (file_exists($original_file) ? '✅ YES' : '❌ NO') . "\n";

// Load the deletion handler
require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/webp-deletion-handler.php');

if (!class_exists('Redco_WebP_Deletion_Handler')) {
    echo "❌ Deletion handler class not found\n";
    exit;
}

echo "✅ Deletion handler class loaded\n";

// Create an instance
$handler = new Redco_WebP_Deletion_Handler();

// Test the cleanup method manually
echo "\n🧪 Testing manual cleanup...\n";

// Use reflection to access the private method
$reflection = new ReflectionClass($handler);
$cleanup_method = $reflection->getMethod('cleanup_webp_files');
$cleanup_method->setAccessible(true);

// Call the cleanup method
$result = $cleanup_method->invoke($handler, $webp_attachment->ID, $conversion_data);

echo "✅ Cleanup method executed\n";
echo "📊 Result: " . print_r($result, true) . "\n";

// Check if files were actually deleted
echo "\n🔍 Checking file status after cleanup:\n";
echo "📁 WebP exists: " . (file_exists($conversion_data['webp_path']) ? '❌ STILL EXISTS' : '✅ DELETED') . "\n";
echo "📁 Original exists: " . (file_exists($original_file) ? '✅ STILL EXISTS' : '❌ DELETED') . "\n";

// Check if metadata was cleaned up
$remaining_data = get_post_meta($webp_attachment->ID, '_webp_conversion_data', true);
echo "📊 Metadata cleaned: " . (empty($remaining_data) ? '✅ YES' : '❌ NO') . "\n";

echo "\n=== Manual Test Complete ===\n";
