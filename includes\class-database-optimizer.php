<?php
/**
 * Database Optimizer Class
 *
 * @package RedcoOptimizer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Database optimization functionality
 */
class Redco_Database_Optimizer {
    
    /**
     * Initialize the database optimizer
     */
    public static function init() {
        // Hook into WordPress
        add_action('admin_init', array(__CLASS__, 'admin_init'));
    }
    
    /**
     * Admin initialization
     */
    public static function admin_init() {
        // Add admin hooks here if needed
    }
    
    /**
     * Optimize database tables
     */
    public static function optimize_tables() {
        global $wpdb;
        
        // Get all WordPress tables
        $tables = $wpdb->get_results("SHOW TABLES", ARRAY_N);
        
        $optimized = 0;
        foreach ($tables as $table) {
            $table_name = $table[0];
            
            // Only optimize WordPress tables
            if (strpos($table_name, $wpdb->prefix) === 0) {
                $result = $wpdb->query("OPTIMIZE TABLE `{$table_name}`");
                if ($result !== false) {
                    $optimized++;
                }
            }
        }
        
        return $optimized;
    }
    
    /**
     * Clean up database
     */
    public static function cleanup_database() {
        global $wpdb;
        
        $cleaned = 0;
        
        // Clean up revisions
        $revisions = $wpdb->query("DELETE FROM {$wpdb->posts} WHERE post_type = 'revision'");
        $cleaned += $revisions;
        
        // Clean up spam comments
        $spam_comments = $wpdb->query("DELETE FROM {$wpdb->comments} WHERE comment_approved = 'spam'");
        $cleaned += $spam_comments;
        
        // Clean up trash comments
        $trash_comments = $wpdb->query("DELETE FROM {$wpdb->comments} WHERE comment_approved = 'trash'");
        $cleaned += $trash_comments;
        
        // Clean up orphaned meta
        $orphaned_meta = $wpdb->query("
            DELETE pm FROM {$wpdb->postmeta} pm
            LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE p.ID IS NULL
        ");
        $cleaned += $orphaned_meta;
        
        return $cleaned;
    }
    
    /**
     * Get database size
     */
    public static function get_database_size() {
        global $wpdb;
        
        $result = $wpdb->get_var("
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size in MB'
            FROM information_schema.tables
            WHERE table_schema = '{$wpdb->dbname}'
        ");
        
        return $result ? $result : 0;
    }
    
    /**
     * Get table information
     */
    public static function get_table_info() {
        global $wpdb;
        
        $tables = $wpdb->get_results("
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb',
                table_rows
            FROM information_schema.TABLES
            WHERE table_schema = '{$wpdb->dbname}'
            AND table_name LIKE '{$wpdb->prefix}%'
            ORDER BY (data_length + index_length) DESC
        ");
        
        return $tables;
    }
}
