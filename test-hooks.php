<?php
/**
 * Test if WebP deletion hooks are properly registered
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Testing WebP Deletion Hooks ===\n\n";

// Check if the WebP module is enabled
$enabled_modules = get_option('redco_optimizer_enabled_modules', array());
$webp_enabled = in_array('smart-webp-conversion', $enabled_modules);

echo "1. Module Status:\n";
echo "   WebP Module Enabled: " . ($webp_enabled ? '✅ YES' : '❌ NO') . "\n\n";

if (!$webp_enabled) {
    echo "❌ WebP module is not enabled. Hooks will not be registered.\n";
    echo "Please enable the Smart WebP Conversion module first.\n";
    exit;
}

// Check if the WebP class file exists
$webp_class_file = 'wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php';
if (!file_exists($webp_class_file)) {
    echo "❌ WebP class file not found: {$webp_class_file}\n";
    exit;
}

echo "2. Class File: ✅ Found\n\n";

// Load the WebP class
require_once($webp_class_file);

// Check if class exists
if (!class_exists('Redco_Smart_WebP_Conversion')) {
    echo "❌ WebP class not found\n";
    exit;
}

echo "3. Class: ✅ Loaded\n\n";

// Check global hooks
global $wp_filter;

echo "4. Hook Registration Check:\n";

$hooks_to_check = [
    'wp_delete_post',
    'deleted_post',
    'before_delete_post',
    'delete_attachment',
    'wp_delete_attachment_files'
];

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "   ✅ {$hook}: " . count($wp_filter[$hook]->callbacks) . " callbacks\n";
        
        // Check for WebP callbacks
        $webp_found = false;
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'Redco_Smart_WebP_Conversion') {
                    echo "      - WebP: {$callback['function'][1]} (priority: {$priority})\n";
                    $webp_found = true;
                }
            }
        }
        
        if (!$webp_found) {
            echo "      ⚠️ No WebP callbacks found\n";
        }
    } else {
        echo "   ❌ {$hook}: No callbacks registered\n";
    }
}

echo "\n5. Manual Hook Test:\n";

// Try to manually instantiate the WebP class
try {
    $webp = new Redco_Smart_WebP_Conversion();
    echo "   ✅ WebP instance created successfully\n";
    
    // Check if hooks are now registered
    if (isset($wp_filter['wp_delete_post'])) {
        $webp_found = false;
        foreach ($wp_filter['wp_delete_post']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'Redco_Smart_WebP_Conversion') {
                    echo "   ✅ WebP deletion hooks now registered\n";
                    $webp_found = true;
                    break 2;
                }
            }
        }
        
        if (!$webp_found) {
            echo "   ❌ WebP deletion hooks still not found\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Failed to create WebP instance: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
