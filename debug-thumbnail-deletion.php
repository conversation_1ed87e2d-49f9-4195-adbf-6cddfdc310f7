<?php
/**
 * Debug thumbnail deletion issue
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Debug Thumbnail Deletion Issue ===\n\n";

// Check for remaining thumbnails
$upload_dir = wp_upload_dir();
$current_dir = $upload_dir['path'];

$files = scandir($current_dir);
$thumbnail_files = array_filter($files, function($file) {
    return preg_match('/final-test-image.*\.(jpg|jpeg|png|gif)$/i', $file);
});

if (!empty($thumbnail_files)) {
    echo "Found remaining thumbnail files:\n";
    foreach ($thumbnail_files as $file) {
        $file_path = $current_dir . '/' . $file;
        echo "   - " . $file . " (" . filesize($file_path) . " bytes)\n";
        echo "     Full path: " . $file_path . "\n";
        
        // Try to delete it manually to test
        if (@unlink($file_path)) {
            echo "     ✅ Successfully deleted manually\n";
        } else {
            echo "     ❌ Failed to delete manually\n";
        }
    }
} else {
    echo "✅ No remaining thumbnail files found\n";
}

// Check for any WebP files
$webp_files = array_filter($files, function($file) {
    return preg_match('/final-test-image.*\.webp$/i', $file);
});

if (!empty($webp_files)) {
    echo "\nFound remaining WebP files:\n";
    foreach ($webp_files as $file) {
        $file_path = $current_dir . '/' . $file;
        echo "   - " . $file . " (" . filesize($file_path) . " bytes)\n";
        
        // Delete WebP files too
        if (@unlink($file_path)) {
            echo "     ✅ Successfully deleted WebP file\n";
        } else {
            echo "     ❌ Failed to delete WebP file\n";
        }
    }
} else {
    echo "\n✅ No remaining WebP files found\n";
}

echo "\n=== Cleanup Complete ===\n";
