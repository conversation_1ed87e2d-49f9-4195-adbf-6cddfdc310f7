<?php
/**
 * Remove all debug logging from WebP class file
 */

$file_path = 'wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php';
$content = file_get_contents($file_path);

echo "Removing debug logging from WebP class file...\n";

// Remove all debug logging blocks
$patterns = [
    // Remove any WP_DEBUG conditional blocks with error_log
    '/\s*if \(defined\(\'WP_DEBUG\'\) && WP_DEBUG\) \{\s*error_log\([^;]*\);\s*\}/s',
    '/\s*if \(defined\(\'WP_DEBUG\'\) && WP_DEBUG\) \{\s*error_log\([^;]*\);\s*error_log\([^;]*\);\s*\}/s',

    // Remove multi-line WP_DEBUG blocks
    '/\s*if \(defined\(\'WP_DEBUG\'\) && WP_DEBUG\) \{[^}]*error_log[^}]*\}/s',

    // Remove empty WP_DEBUG blocks
    '/\s*if \(defined\(\'WP_DEBUG\'\) && WP_DEBUG\) \{\s*\}/s',

    // Remove standalone error_log with emojis
    '/\s*error_log\([^)]*[🔧🔄✅❌🔥⚠️🔍🚨][^)]*\);\s*/s',

    // Remove debug comments
    '/\s*\/\/ CRITICAL DEBUG:[^\n]*\n/s',
    '/\s*\/\/ DEBUG:[^\n]*\n/s',
    '/\s*\/\/ Debug[^\n]*\n/s',
];

$original_size = strlen($content);

foreach ($patterns as $pattern) {
    $content = preg_replace($pattern, '', $content);
}

// Clean up extra blank lines (more than 2 consecutive)
$content = preg_replace('/\n{3,}/', "\n\n", $content);

// Write back to file
file_put_contents($file_path, $content);

$new_size = strlen($content);
$reduction = $original_size - $new_size;

echo "✅ Debug logging removed from WebP class file\n";
echo "📊 File size reduced by {$reduction} bytes ({$original_size} -> {$new_size})\n";

// Check if there are any remaining debug statements
$remaining_debug = preg_match_all('/error_log.*[🔧🔄✅❌🔥⚠️🔍]/', $content);
if ($remaining_debug > 0) {
    echo "⚠️ Warning: {$remaining_debug} debug statements may still remain\n";
} else {
    echo "✅ All emoji debug logging appears to be removed\n";
}

// Check for any remaining WP_DEBUG blocks
$remaining_wp_debug = preg_match_all('/if \(defined\(\'WP_DEBUG\'\) && WP_DEBUG\)/', $content);
if ($remaining_wp_debug > 0) {
    echo "⚠️ Warning: {$remaining_wp_debug} WP_DEBUG blocks may still remain\n";
} else {
    echo "✅ All WP_DEBUG blocks appear to be removed\n";
}
?>
