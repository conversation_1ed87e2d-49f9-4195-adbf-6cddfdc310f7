<?php
/**
 * Debug WebP module initialization and hooks
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== WebP Module Debug ===\n\n";

// 1. Check if WebP module is enabled
echo "1. Module Status:\n";

if (function_exists('redco_is_module_enabled')) {
    $webp_enabled = redco_is_module_enabled('smart-webp-conversion');
    echo "   WebP Module Enabled: " . ($webp_enabled ? 'YES' : 'NO') . "\n";
    
    if (!$webp_enabled) {
        echo "   🔧 Enabling WebP module...\n";
        
        // Enable the module
        $enabled_modules = get_option('redco_enabled_modules', array());
        if (!in_array('smart-webp-conversion', $enabled_modules)) {
            $enabled_modules[] = 'smart-webp-conversion';
            update_option('redco_enabled_modules', $enabled_modules);
            echo "   ✅ WebP module enabled\n";
        }
    }
} else {
    echo "   ❌ redco_is_module_enabled function not found\n";
}

// 2. Check WebP class initialization
echo "\n2. WebP Class Status:\n";

if (class_exists('Redco_Smart_WebP_Conversion')) {
    echo "   ✅ Redco_Smart_WebP_Conversion class exists\n";
    
    $webp = new Redco_Smart_WebP_Conversion();
    echo "   ✅ WebP instance created\n";
    
    // Check if auto-convert hook is registered
    global $wp_filter;
    
    if (isset($wp_filter['wp_generate_attachment_metadata'])) {
        echo "   📊 wp_generate_attachment_metadata hooks:\n";
        foreach ($wp_filter['wp_generate_attachment_metadata']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && is_object($callback['function'][0])) {
                    $class_name = get_class($callback['function'][0]);
                    $method_name = $callback['function'][1];
                    echo "      - Priority {$priority}: {$class_name}::{$method_name}\n";
                } else {
                    echo "      - Priority {$priority}: " . print_r($callback['function'], true) . "\n";
                }
            }
        }
    }
    
} else {
    echo "   ❌ Redco_Smart_WebP_Conversion class not found\n";
    
    // Try to load it manually
    $class_file = 'wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php';
    if (file_exists($class_file)) {
        require_once($class_file);
        echo "   ✅ WebP class file loaded manually\n";
        
        if (class_exists('Redco_Smart_WebP_Conversion')) {
            echo "   ✅ Class now available\n";
        } else {
            echo "   ❌ Class still not available after loading\n";
        }
    } else {
        echo "   ❌ WebP class file not found: {$class_file}\n";
    }
}

// 3. Test manual conversion
echo "\n3. Manual Conversion Test:\n";

if (class_exists('Redco_Smart_WebP_Conversion')) {
    $webp = new Redco_Smart_WebP_Conversion();
    
    // Check WebP support
    echo "   WebP Support: " . ($webp->can_convert_webp() ? 'YES' : 'NO') . "\n";
    
    // Create a simple test image
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    require_once(ABSPATH . 'wp-admin/includes/file.php');
    require_once(ABSPATH . 'wp-admin/includes/media.php');
    
    $upload_dir = wp_upload_dir();
    $test_image_path = $upload_dir['path'] . '/manual-test.jpg';
    
    $image = imagecreate(100, 100);
    $white = imagecolorallocate($image, 255, 255, 255);
    $blue = imagecolorallocate($image, 50, 50, 200);
    imagefill($image, 0, 0, $white);
    imagestring($image, 3, 25, 45, 'MANUAL', $blue);
    imagejpeg($image, $test_image_path, 90);
    imagedestroy($image);
    
    echo "   ✅ Test image created: " . basename($test_image_path) . "\n";
    
    // Try manual conversion
    try {
        $webp_path = $webp->convert_to_webp($test_image_path);
        if ($webp_path && file_exists($webp_path)) {
            echo "   ✅ Manual WebP conversion successful: " . basename($webp_path) . "\n";
            
            // Clean up
            @unlink($test_image_path);
            @unlink($webp_path);
            echo "   ✅ Test files cleaned up\n";
        } else {
            echo "   ❌ Manual WebP conversion failed\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Manual conversion exception: " . $e->getMessage() . "\n";
    }
}

// 4. Check plugin initialization
echo "\n4. Plugin Initialization:\n";

// Check if main plugin file is loaded
if (defined('REDCO_OPTIMIZER_PLUGIN_DIR')) {
    echo "   ✅ Main plugin constants defined\n";
    echo "   📁 Plugin dir: " . REDCO_OPTIMIZER_PLUGIN_DIR . "\n";
} else {
    echo "   ❌ Main plugin constants not defined\n";
}

// Check if module loader is working
if (function_exists('redco_load_modules')) {
    echo "   ✅ Module loader function exists\n";
} else {
    echo "   ❌ Module loader function not found\n";
}

// 5. Force module initialization
echo "\n5. Force Module Initialization:\n";

// Try to manually initialize the WebP module
$module_file = 'wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/smart-webp-conversion.php';
if (file_exists($module_file)) {
    echo "   ✅ Module file exists: " . basename($module_file) . "\n";
    
    // Include the module file
    require_once($module_file);
    echo "   ✅ Module file included\n";
    
    // Check if hooks are now registered
    global $wp_filter;
    if (isset($wp_filter['wp_generate_attachment_metadata'])) {
        $hook_count = 0;
        foreach ($wp_filter['wp_generate_attachment_metadata']->callbacks as $priority => $callbacks) {
            $hook_count += count($callbacks);
        }
        echo "   📊 Total wp_generate_attachment_metadata hooks: {$hook_count}\n";
    }
    
} else {
    echo "   ❌ Module file not found: {$module_file}\n";
}

echo "\n=== Debug Complete ===\n";
