<?php
/**
 * Manually trigger auto-convert and test deletion
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Manual Trigger Auto-Convert Test ===\n\n";

// Include required WordPress functions
require_once(ABSPATH . 'wp-admin/includes/image.php');
require_once(ABSPATH . 'wp-admin/includes/file.php');
require_once(ABSPATH . 'wp-admin/includes/media.php');

// 1. Create test image and attachment
echo "1. Creating Test Image and Attachment:\n";

$upload_dir = wp_upload_dir();
$test_image_path = $upload_dir['path'] . '/manual-trigger-test.jpg';

$image = imagecreate(200, 150);
$white = imagecolorallocate($image, 255, 255, 255);
$orange = imagecolorallocate($image, 200, 100, 50);
imagefill($image, 0, 0, $white);
imagestring($image, 5, 40, 70, 'TRIGGER', $orange);
imagejpeg($image, $test_image_path, 90);
imagedestroy($image);

echo "   ✅ Test image created: " . basename($test_image_path) . "\n";

$attachment_data = array(
    'post_mime_type' => 'image/jpeg',
    'post_title' => 'Manual Trigger Test',
    'post_content' => '',
    'post_status' => 'inherit'
);

$attachment_id = wp_insert_attachment($attachment_data, $test_image_path);
$metadata = wp_generate_attachment_metadata($attachment_id, $test_image_path);
wp_update_attachment_metadata($attachment_id, $metadata);

echo "   ✅ Attachment created with ID: {$attachment_id}\n";

// 2. Manually trigger auto-convert
echo "\n2. Manually Triggering Auto-Convert:\n";

require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php');
$webp = new Redco_Smart_WebP_Conversion();

// Manually call the auto-convert method
echo "   🔄 Calling auto_convert_after_complete_upload() directly...\n";
$result_metadata = $webp->auto_convert_after_complete_upload($metadata, $attachment_id);

// Check if conversion happened
$conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

if ($conversion_data && isset($conversion_data['converted']) && $conversion_data['converted']) {
    echo "   🎉 MANUAL AUTO-CONVERSION SUCCESSFUL!\n";
    echo "   📊 Method: " . (isset($conversion_data['method']) ? $conversion_data['method'] : 'unknown') . "\n";
    echo "   📊 Architecture: " . (isset($conversion_data['original_preserved']) && $conversion_data['original_preserved'] ? 'NEW' : 'LEGACY') . "\n";
    
    // 3. Check the stored file_path for corruption
    echo "\n3. Checking Stored File Path:\n";
    
    if (isset($conversion_data['file_path'])) {
        echo "   📁 Stored file_path: " . $conversion_data['file_path'] . "\n";
        echo "   📁 Original test path: " . $test_image_path . "\n";
        echo "   📁 Paths match: " . ($conversion_data['file_path'] === $test_image_path ? '✅ YES' : '❌ NO') . "\n";
        echo "   📁 File exists: " . (file_exists($conversion_data['file_path']) ? '✅ YES' : '❌ NO') . "\n";
        
        // Check for path corruption
        if (strpos($conversion_data['file_path'], 'D:xampp') !== false) {
            echo "   ⚠️ PATH CORRUPTION DETECTED! Missing backslashes\n";
            echo "   🔧 Expected: D:\\xampp\\htdocs\\...\n";
            echo "   🔧 Got: " . $conversion_data['file_path'] . "\n";
        } else {
            echo "   ✅ Path appears correctly formatted\n";
        }
    } else {
        echo "   ❌ file_path not found in conversion data\n";
    }
    
    // 4. Test deletion with real conversion data
    echo "\n4. Testing Deletion with Real Conversion Data:\n";
    
    // Load deletion handlers
    require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/webp-global-handlers.php');
    
    // Count files before deletion
    $files_before = 0;
    $original_files = array();
    $webp_files = array();
    
    // Original main file
    if (isset($conversion_data['file_path']) && file_exists($conversion_data['file_path'])) {
        $files_before++;
        $original_files[] = $conversion_data['file_path'];
    }
    
    // WebP main file
    if (isset($conversion_data['webp_path']) && file_exists($conversion_data['webp_path'])) {
        $files_before++;
        $webp_files[] = $conversion_data['webp_path'];
    }
    
    // Original thumbnails
    foreach ($metadata['sizes'] as $size_name => $size_data) {
        $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
        if (file_exists($thumb_path)) {
            $files_before++;
            $original_files[] = $thumb_path;
        }
    }
    
    // WebP thumbnails
    if (isset($conversion_data['webp_sizes'])) {
        foreach ($conversion_data['webp_sizes'] as $size_name => $webp_size_path) {
            if (file_exists($webp_size_path)) {
                $files_before++;
                $webp_files[] = $webp_size_path;
            }
        }
    }
    
    echo "   📊 Files before deletion: {$files_before}\n";
    echo "   📊 Original files: " . count($original_files) . "\n";
    echo "   📊 WebP files: " . count($webp_files) . "\n";
    
    echo "   🗑️ Deleting attachment {$attachment_id}...\n";
    
    $deleted = wp_delete_attachment($attachment_id, true);
    
    if ($deleted) {
        echo "   ✅ WordPress deletion completed\n";
    } else {
        echo "   ❌ WordPress deletion failed\n";
    }
    
    // 5. Check files after deletion
    echo "\n5. Files After Deletion:\n";
    
    $files_after = 0;
    $original_remaining = 0;
    $webp_remaining = 0;
    
    echo "   Original files:\n";
    foreach ($original_files as $file) {
        $exists = file_exists($file);
        echo "      - " . basename($file) . ": " . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . "\n";
        if ($exists) {
            $files_after++;
            $original_remaining++;
        }
    }
    
    echo "   WebP files:\n";
    foreach ($webp_files as $file) {
        $exists = file_exists($file);
        echo "      - " . basename($file) . ": " . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . "\n";
        if ($exists) {
            $files_after++;
            $webp_remaining++;
        }
    }
    
    echo "   📊 Files after deletion: {$files_after}\n";
    echo "   📊 Original files remaining: {$original_remaining}\n";
    echo "   📊 WebP files remaining: {$webp_remaining}\n";
    
    // Check metadata cleanup
    $remaining_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
    if ($remaining_data) {
        echo "   📊 Conversion metadata: ❌ STILL EXISTS\n";
    } else {
        echo "   📊 Conversion metadata: ✅ CLEANED UP\n";
    }
    
    // 6. Final result
    echo "\n=== FINAL RESULT ===\n";
    
    if ($files_after === 0 && !$remaining_data) {
        echo "🎉 PERFECT! Real auto-conversion and deletion working 100%!\n";
        echo "✅ All {$files_before} files deleted successfully\n";
        echo "✅ All conversion metadata cleaned up\n";
        
        if (strpos($conversion_data['file_path'], 'D:xampp') !== false) {
            echo "⚠️ BUT path corruption was detected in conversion data\n";
            echo "🔧 This means the fix needs to be applied to the conversion process\n";
        } else {
            echo "✅ No path corruption detected\n";
        }
    } else {
        echo "❌ Issues found:\n";
        if ($files_after > 0) {
            echo "   - {$files_after} out of {$files_before} files were not deleted\n";
            if ($original_remaining > 0) {
                echo "   - {$original_remaining} original files not deleted (path corruption issue)\n";
            }
            if ($webp_remaining > 0) {
                echo "   - {$webp_remaining} WebP files not deleted\n";
            }
        }
        if ($remaining_data) {
            echo "   - Conversion metadata was not cleaned up\n";
        }
        
        echo "\n🔧 Check debug logs for detailed information\n";
    }
    
} else {
    echo "   ❌ Manual auto-conversion failed\n";
    echo "   🔧 Check WebP settings and debug logs\n";
}

echo "\n🔧 This test manually triggers the real auto-conversion process\n";
