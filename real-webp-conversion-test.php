<?php
/**
 * Test REAL WebP conversion process (not manual)
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Include required WordPress functions
require_once(ABSPATH . 'wp-admin/includes/image.php');
require_once(ABSPATH . 'wp-admin/includes/file.php');
require_once(ABSPATH . 'wp-admin/includes/media.php');

echo "=== Real WebP Conversion Test ===\n\n";

// 1. Create test image
echo "1. Creating Test Image:\n";

$upload_dir = wp_upload_dir();
$test_image_path = $upload_dir['path'] . '/real-conversion-test.jpg';

$image = imagecreate(250, 180);
$white = imagecolorallocate($image, 255, 255, 255);
$green = imagecolorallocate($image, 50, 150, 50);
imagefill($image, 0, 0, $white);
imagestring($image, 5, 60, 80, 'REAL TEST', $green);
imagejpeg($image, $test_image_path, 90);
imagedestroy($image);

echo "   ✅ Test image created: " . basename($test_image_path) . "\n";
echo "   📁 Path: " . $test_image_path . "\n";

// 2. Create WordPress attachment
echo "\n2. Creating WordPress Attachment:\n";

$attachment_data = array(
    'post_mime_type' => 'image/jpeg',
    'post_title' => 'Real Conversion Test',
    'post_content' => '',
    'post_status' => 'inherit'
);

$attachment_id = wp_insert_attachment($attachment_data, $test_image_path);
$metadata = wp_generate_attachment_metadata($attachment_id, $test_image_path);
wp_update_attachment_metadata($attachment_id, $metadata);

echo "   ✅ Attachment created with ID: {$attachment_id}\n";

// 3. Use REAL WebP conversion process
echo "\n3. Using REAL WebP Conversion Process:\n";

require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php');
$webp = new Redco_Smart_WebP_Conversion();

// Use the REAL auto-convert method that the plugin actually uses
echo "   🔄 Calling auto_convert_after_complete_upload() method...\n";

// This is the REAL method that gets called when images are uploaded
$result_metadata = $webp->auto_convert_after_complete_upload($metadata, $attachment_id);

// Check if conversion was successful
$conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

if ($conversion_data && isset($conversion_data['converted']) && $conversion_data['converted']) {
    echo "   ✅ Real WebP auto-conversion successful!\n";
    echo "   📊 Conversion method: " . (isset($conversion_data['method']) ? $conversion_data['method'] : 'unknown') . "\n";
} else {
    echo "   ❌ Real WebP auto-conversion failed or no conversion data found\n";
    echo "   🔧 This might be because auto-convert is disabled\n";
    exit;
}

// 4. Check the REAL conversion data structure
echo "4. Checking REAL Conversion Data:\n";

$conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
if ($conversion_data) {
    echo "   ✅ Conversion data found\n";
    echo "   📊 Architecture: " . (isset($conversion_data['original_preserved']) && $conversion_data['original_preserved'] ? 'NEW' : 'LEGACY') . "\n";
    
    // Check the stored file_path
    if (isset($conversion_data['file_path'])) {
        echo "   📁 Stored file_path: " . $conversion_data['file_path'] . "\n";
        echo "   📁 File exists: " . (file_exists($conversion_data['file_path']) ? '✅ YES' : '❌ NO') . "\n";
        
        // Check if the path is corrupted
        if (strpos($conversion_data['file_path'], 'D:xampp') !== false) {
            echo "   ⚠️ PATH CORRUPTION DETECTED! Missing backslashes after drive letter\n";
        } else {
            echo "   ✅ Path appears to be correctly formatted\n";
        }
    } else {
        echo "   ❌ file_path not found in conversion data\n";
    }
    
    // Check WebP path
    if (isset($conversion_data['webp_path'])) {
        echo "   📁 WebP path: " . $conversion_data['webp_path'] . "\n";
        echo "   📁 WebP exists: " . (file_exists($conversion_data['webp_path']) ? '✅ YES' : '❌ NO') . "\n";
    }
    
} else {
    echo "   ❌ No conversion data found\n";
    exit;
}

// 5. Test deletion with REAL conversion data
echo "\n5. Testing Deletion with REAL Conversion Data:\n";

// Load deletion handlers
require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/webp-global-handlers.php');

// Count files before deletion
$files_before = 0;
if (isset($conversion_data['file_path']) && file_exists($conversion_data['file_path'])) $files_before++;
if (isset($conversion_data['webp_path']) && file_exists($conversion_data['webp_path'])) $files_before++;
foreach ($metadata['sizes'] as $size_data) {
    $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
    if (file_exists($thumb_path)) $files_before++;
}
if (isset($conversion_data['webp_sizes'])) {
    foreach ($conversion_data['webp_sizes'] as $webp_size_path) {
        if (file_exists($webp_size_path)) $files_before++;
    }
}

echo "   📊 Files before deletion: {$files_before}\n";

echo "   🗑️ Deleting attachment {$attachment_id}...\n";

// Delete the attachment
$deleted = wp_delete_attachment($attachment_id, true);

if ($deleted) {
    echo "   ✅ WordPress deletion completed\n";
} else {
    echo "   ❌ WordPress deletion failed\n";
}

// 6. Check files after deletion
echo "\n6. Files After Deletion:\n";

$files_after = 0;

// Check original file
if (isset($conversion_data['file_path'])) {
    $exists = file_exists($conversion_data['file_path']);
    echo "   📁 Original main: " . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . " - " . basename($conversion_data['file_path']) . "\n";
    if ($exists) $files_after++;
}

// Check WebP file
if (isset($conversion_data['webp_path'])) {
    $exists = file_exists($conversion_data['webp_path']);
    echo "   📁 WebP main: " . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . " - " . basename($conversion_data['webp_path']) . "\n";
    if ($exists) $files_after++;
}

// Check original thumbnails
foreach ($metadata['sizes'] as $size_name => $size_data) {
    $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
    $exists = file_exists($thumb_path);
    echo "   📁 Original thumb ({$size_name}): " . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . " - " . $size_data['file'] . "\n";
    if ($exists) $files_after++;
}

// Check WebP thumbnails
if (isset($conversion_data['webp_sizes'])) {
    foreach ($conversion_data['webp_sizes'] as $size_name => $webp_size_path) {
        $exists = file_exists($webp_size_path);
        echo "   📁 WebP thumb ({$size_name}): " . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . " - " . basename($webp_size_path) . "\n";
        if ($exists) $files_after++;
    }
}

echo "   📊 Files after deletion: {$files_after}\n";

// Check metadata cleanup
$remaining_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
if ($remaining_data) {
    echo "   📊 Conversion metadata: ❌ STILL EXISTS\n";
} else {
    echo "   📊 Conversion metadata: ✅ CLEANED UP\n";
}

// 7. Final result
echo "\n=== FINAL RESULT ===\n";

if ($files_after === 0 && !$remaining_data) {
    echo "🎉 PERFECT! Real WebP conversion and deletion working 100%!\n";
    echo "✅ All {$files_before} files deleted successfully\n";
    echo "✅ All conversion metadata cleaned up\n";
    echo "✅ No path corruption issues\n";
} else {
    echo "❌ Issues found:\n";
    if ($files_after > 0) {
        echo "   - {$files_after} out of {$files_before} files were not deleted\n";
    }
    if ($remaining_data) {
        echo "   - Conversion metadata was not cleaned up\n";
    }
    
    echo "\n🔧 Check the debug logs for detailed information\n";
}

echo "\n🔧 This test uses the REAL WebP conversion process, not manual data creation\n";
