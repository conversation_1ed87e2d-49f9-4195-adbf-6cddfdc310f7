<?php
/**
 * Helper functions for Redco Optimizer
 *
 * Contains utility functions used throughout the plugin.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get plugin options
 */
function redco_get_option($option_name, $default = null) {
    // Ensure option_name is not null to prevent warnings
    if ($option_name === null || $option_name === '') {
        return $default;
    }

    $options = get_option('redco_optimizer_options', array());
    return isset($options[$option_name]) ? $options[$option_name] : $default;
}

/**
 * Update plugin option
 */
function redco_update_option($option_name, $value) {
    // Ensure option_name is not null to prevent warnings
    if ($option_name === null || $option_name === '') {
        return false;
    }

    $options = get_option('redco_optimizer_options', array());
    $options[$option_name] = $value;
    return update_option('redco_optimizer_options', $options);
}

/**
 * Get module settings - handles both old and new format
 */
function redco_get_module_option($module, $option_name, $default = null) {
    // Ensure module and option_name are not null to prevent warnings
    if ($module === null || $module === '' || $option_name === null || $option_name === '') {
        return $default;
    }

    // Ensure module is a string to avoid null parameter warnings
    $module = (string) $module;
    $module_key = 'redco_optimizer_' . str_replace('-', '_', $module);
    $options = get_option($module_key, array());

    // Handle new nested format (settings[field_name])
    if ($option_name === 'settings' && isset($options['settings'])) {
        return $options['settings'];
    }

    $value = null;

    // Handle old direct format or fallback
    if (isset($options[$option_name])) {
        $value = $options[$option_name];
    }
    // Check if data is nested under 'settings' key
    elseif (isset($options['settings']) && isset($options['settings'][$option_name])) {
        $value = $options['settings'][$option_name];
    }
    else {
        return $default;
    }

    // WEBP FIX: Convert checkbox values to proper boolean format
    if ($module === 'smart-webp-conversion') {
        // Convert various checkbox value formats to boolean
        if ($value === '0' || $value === 0 || $value === false || $value === 'false') {
            return false;
        }
        if ($value === '1' || $value === 1 || $value === true || $value === 'true' || $value === 'on') {
            return true;
        }

        // For numeric values (like quality), return as-is
        if (is_numeric($value)) {
            return $value;
        }

        // Debug logging for WebP module
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔧 WEBP HELPER DEBUG: Converting value for $option_name: " . print_r($value, true) . " -> " . print_r($value, true));
        }
    }

    return $value;
}

/**
 * Update module setting with validation
 */
function redco_update_module_option($module, $option_name, $value) {
    // Ensure module and option_name are not null to prevent warnings
    if ($module === null || $module === '' || $option_name === null || $option_name === '') {
        return false;
    }

    // Ensure module is a string to avoid null parameter warnings
    $module = (string) $module;
    $module_key = 'redco_optimizer_' . str_replace('-', '_', $module);
    $options = get_option($module_key, array());
    $options[$option_name] = $value;

    // Validate settings using the settings validator
    $validated_options = Redco_Settings_Validator::validate_module_settings($module, $options);

    return update_option($module_key, $validated_options);
}

/**
 * Check if module is enabled
 */
function redco_is_module_enabled($module) {
    // Ensure module is not null to prevent warnings
    if ($module === null || $module === '') {
        return false;
    }

    try {
        $enabled_modules = redco_get_option('modules_enabled', array());

        // Safety check: ensure we have an array
        if (!is_array($enabled_modules)) {
            error_log("Redco Helper: modules_enabled option is not an array: " . gettype($enabled_modules));
            return false;
        }

        return in_array($module, $enabled_modules);
    } catch (Exception $e) {
        error_log("Redco Helper: Error checking if module {$module} is enabled: " . $e->getMessage());
        return false;
    }
}

/**
 * Get file size in human readable format
 */
function redco_format_bytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }

    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * Get database size
 */
function redco_get_database_size() {
    global $wpdb;

    $size = 0;
    $tables = $wpdb->get_results("SHOW TABLE STATUS", ARRAY_A);

    if ($tables) {
        foreach ($tables as $table) {
            $size += $table['Data_length'] + $table['Index_length'];
        }
    }

    return $size;
}

/**
 * Get cache directory path using centralized configuration
 */
function redco_get_cache_dir($type = 'cache') {
    // Use centralized configuration for cache directories
    $cache_dir = Redco_Config::get_cache_dir($type);

    // Create directory if it doesn't exist
    if (!file_exists($cache_dir)) {
        wp_mkdir_p($cache_dir);

        // Add .htaccess for security
        $htaccess_content = "# Redco Optimizer Cache Directory\n";
        $htaccess_content .= "Options -Indexes\n";
        $htaccess_content .= "<Files *.php>\n";
        $htaccess_content .= "deny from all\n";
        $htaccess_content .= "</Files>\n";

        file_put_contents($cache_dir . '.htaccess', $htaccess_content);
    }

    return $cache_dir;
}

/**
 * Clear cache directory recursively
 */
function redco_clear_cache() {
    $cache_dir = redco_get_cache_dir();

    if (is_dir($cache_dir)) {
        return redco_clear_directory_recursive($cache_dir, false); // Don't remove the main cache directory
    }

    return false;
}

/**
 * Clear directory recursively with improved error handling
 */
function redco_clear_directory_recursive($dir, $remove_dir = true) {
    // Ensure directory path is not null or empty to prevent wp_is_stream() warnings
    if (!$dir || empty($dir) || !is_string($dir)) {
        error_log("Redco Optimizer: Invalid directory path provided");
        return false;
    }

    if (!is_dir($dir)) {
        error_log("Redco Optimizer: Directory does not exist: {$dir}");
        return false;
    }

    // Check if directory is writable
    if (!is_writable($dir)) {
        error_log("Redco Optimizer: Directory is not writable: {$dir}");
        return false;
    }

    $files = array_diff(scandir($dir), array('.', '..'));
    $success = true;
    $errors = array();

    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;

        if (is_dir($path)) {
            // Recursively clear subdirectory
            if (!redco_clear_directory_recursive($path, true)) {
                $success = false;
                $errors[] = "Failed to clear subdirectory: {$path}";
            }
        } else {
            // Check if file is writable before attempting to delete
            if (!is_writable($path)) {
                $success = false;
                $errors[] = "File is not writable: {$path}";
                continue;
            }

            // Delete file
            if (!unlink($path)) {
                $success = false;
                $errors[] = "Failed to delete file: {$path}";
            }
        }
    }

    // Remove the directory itself if requested
    if ($remove_dir && $success) {
        if (!rmdir($dir)) {
            $success = false;
            $errors[] = "Failed to remove directory: {$dir}";
        }
    }

    // Log errors if any occurred
    if (!empty($errors)) {
        error_log("Redco Optimizer: Cache clearing errors: " . implode(', ', $errors));
    }

    return $success;
}

/**
 * Get WordPress memory limit
 */
function redco_get_memory_limit() {
    $memory_limit = ini_get('memory_limit');

    if (preg_match('/^(\d+)(.)$/', $memory_limit, $matches)) {
        if ($matches[2] == 'M') {
            $memory_limit = $matches[1] * 1024 * 1024;
        } elseif ($matches[2] == 'K') {
            $memory_limit = $matches[1] * 1024;
        } elseif ($matches[2] == 'G') {
            $memory_limit = $matches[1] * 1024 * 1024 * 1024;
        }
    }

    return $memory_limit;
}

/**
 * Get current memory usage
 */
function redco_get_memory_usage() {
    return memory_get_usage(true);
}

/**
 * Get peak memory usage
 */
function redco_get_peak_memory_usage() {
    return memory_get_peak_usage(true);
}

/**
 * Check if WooCommerce is active
 */
function redco_is_woocommerce_active() {
    return class_exists('WooCommerce');
}



/**
 * Get all post types
 */
function redco_get_post_types() {
    $post_types = get_post_types(array('public' => true), 'objects');
    $types = array();

    foreach ($post_types as $post_type) {
        $types[$post_type->name] = $post_type->label;
    }

    return $types;
}

/**
 * Get all pages
 */
function redco_get_pages() {
    $pages = get_pages(array(
        'sort_column' => 'menu_order',
        'sort_order' => 'ASC'
    ));

    $page_list = array();
    foreach ($pages as $page) {
        $page_list[$page->ID] = $page->post_title;
    }

    return $page_list;
}

/**
 * Get enqueued scripts
 */
function redco_get_enqueued_scripts() {
    global $wp_scripts;

    if (!$wp_scripts) {
        return array();
    }

    $scripts = array();
    foreach ($wp_scripts->registered as $handle => $script) {
        $scripts[$handle] = array(
            'handle' => $handle,
            'src' => $script->src,
            'deps' => $script->deps,
            'ver' => $script->ver
        );
    }

    return $scripts;
}

/**
 * Get enqueued styles
 */
function redco_get_enqueued_styles() {
    global $wp_styles;

    if (!$wp_styles) {
        return array();
    }

    $styles = array();
    foreach ($wp_styles->registered as $handle => $style) {
        $styles[$handle] = array(
            'handle' => $handle,
            'src' => $style->src,
            'deps' => $style->deps,
            'ver' => $style->ver
        );
    }

    return $styles;
}

/**
 * Get database cleanup statistics
 */
function redco_get_cleanup_stats() {
    global $wpdb;

    $stats = array();

    // Post revisions
    $stats['revisions'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'revision'");

    // Auto drafts
    $stats['auto_drafts'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'auto-draft'");

    // Trashed posts
    $stats['trashed_posts'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'trash'");

    // Spam comments
    $stats['spam_comments'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_approved = 'spam'");

    // Trashed comments
    $stats['trashed_comments'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_approved = 'trash'");

    // Expired transients
    $stats['expired_transients'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_%' AND option_value < UNIX_TIMESTAMP()");

    // Orphaned postmeta
    $stats['orphaned_postmeta'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->postmeta} pm LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID WHERE p.ID IS NULL");

    // Orphaned commentmeta
    $stats['orphaned_commentmeta'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->commentmeta} cm LEFT JOIN {$wpdb->comments} c ON cm.comment_id = c.comment_ID WHERE c.comment_ID IS NULL");

    return $stats;
}



/**
 * Check if current page is plugin admin page
 */
function redco_is_admin_page() {
    $screen = get_current_screen();
    return $screen && strpos($screen->id, 'redco-optimizer') !== false;
}

/**
 * Get plugin version
 */
function redco_get_version() {
    return REDCO_OPTIMIZER_VERSION;
}

/**
 * Check minimum WordPress version
 */
function redco_check_wp_version($min_version = '5.0') {
    global $wp_version;
    return version_compare($wp_version, $min_version, '>=');
}

/**
 * Check minimum PHP version
 */
function redco_check_php_version($min_version = '7.4') {
    return version_compare(PHP_VERSION, $min_version, '>=');
}

/**
 * Get system information
 */
function redco_get_system_info() {
    global $wp_version;

    return array(
        'wp_version' => $wp_version,
        'php_version' => PHP_VERSION,
        'mysql_version' => $GLOBALS['wpdb']->db_version(),
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size')
    );
}
