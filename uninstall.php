<?php
/**
 * Uninstall script for Redco Optimizer
 * 
 * This file is executed when the plugin is deleted from WordPress admin.
 * It cleans up all plugin data, options, and temporary files.
 */

// Prevent direct access
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

/**
 * Clean up plugin options
 */
function redco_optimizer_cleanup_options() {
    // Remove main plugin options
    delete_option('redco_optimizer_options');
    
    // Remove module-specific options
    $modules = array(
        'page_cache',
        'lazy_load',
        'css_js_minifier',
        'database_cleanup',
        'heartbeat_control',
        'emoji_stripper',
        'query_string_remover',
        'asset_version_remover',
        'autosave_reducer',
        'smart_webp_conversion', // Added WebP module for proper cleanup
        'wordpress_core_tweaks'  // Added consolidated module
    );
    
    foreach ($modules as $module) {
        delete_option('redco_optimizer_' . $module);
    }
    
    // Remove any transients
    delete_transient('redco_optimizer_cache');
    delete_transient('redco_optimizer_db_stats');

    // WEBP CLEANUP: Remove WebP-specific options and transients
    redco_optimizer_cleanup_webp_data();
}

/**
 * Clean up WebP-specific data during uninstall
 */
function redco_optimizer_cleanup_webp_data() {
    // Remove WebP-specific options
    delete_option('redco_webp_recent_conversions');
    delete_option('redco_optimizer_smart_webp_conversion');

    // Remove WebP-related transients
    delete_transient('redco_webp_stats_v2');
    delete_transient('redco_webp_convertible_count');

    // Clean up any remaining WebP cache entries
    global $wpdb;

    // Remove any options that start with 'redco_webp_'
    $wpdb->query("
        DELETE FROM {$wpdb->options}
        WHERE option_name LIKE 'redco_webp_%'
        OR option_name LIKE '_transient_redco_webp_%'
        OR option_name LIKE '_transient_timeout_redco_webp_%'
    ");
}

/**
 * Clean up plugin files and cache
 */
function redco_optimizer_cleanup_files() {
    $upload_dir = wp_upload_dir();
    $cache_dir = $upload_dir['basedir'] . '/redco-optimizer-cache/';
    
    // Remove cache directory if it exists
    if (is_dir($cache_dir)) {
        redco_optimizer_remove_directory($cache_dir);
    }
}

/**
 * Recursively remove directory
 */
function redco_optimizer_remove_directory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            redco_optimizer_remove_directory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}

/**
 * Clean up scheduled events
 */
function redco_optimizer_cleanup_cron() {
    // Remove any scheduled cron events
    wp_clear_scheduled_hook('redco_optimizer_database_cleanup');
    wp_clear_scheduled_hook('redco_optimizer_cache_cleanup');
}

// Load necessary plugin constants and functions for module cleanup
if (!defined('REDCO_OPTIMIZER_PLUGIN_DIR')) {
    define('REDCO_OPTIMIZER_PLUGIN_DIR', plugin_dir_path(__FILE__));
}

if (!defined('REDCO_OPTIMIZER_PLUGIN_URL')) {
    define('REDCO_OPTIMIZER_PLUGIN_URL', plugin_dir_url(__FILE__));
}

// Load WebP module for uninstall recovery (if it exists)
$webp_handlers_file = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/webp-global-handlers.php';
if (file_exists($webp_handlers_file)) {
    require_once $webp_handlers_file;
}

// Execute cleanup
redco_optimizer_cleanup_options();
redco_optimizer_cleanup_files();
redco_optimizer_cleanup_cron();

// Allow modules to perform their own cleanup (including WebP recovery)
do_action('redco_optimizer_uninstall');
