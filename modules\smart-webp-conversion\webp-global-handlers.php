<?php
/**
 * Smart WebP Conversion Global AJAX Handlers
 *
 * Contains global AJAX handlers and initialization functions
 * that work with the Enhanced WebP class only.
 *
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// DEBUG: Log that this file is being loaded
error_log("🔥 WEBP_GLOBAL_HANDLERS: File loaded successfully");

// Global stats AJAX handler (works regardless of module status)
function redco_webp_ajax_get_stats() {
    // CRITICAL FIX: More flexible nonce verification
    $nonce_verified = false;
    if (isset($_POST['nonce'])) {
        $nonce_verified = wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') ||
                         wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce');
    }

    if (!$nonce_verified) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // CRITICAL FIX: Always return real stats when on the module page
    // The module status check was causing zero stats to be returned
    // even when the module was enabled and the user was viewing the module page

    // Get WebP module instance
    global $redco_webp_instance;
    if (!$redco_webp_instance) {
        if (class_exists('Redco_Smart_WebP_Conversion')) {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();
        } else {
            wp_send_json_error('WebP conversion class not available');
            return;
        }
    }

    // Call the instance method
    wp_send_json($redco_webp_instance->get_stats());
}

// Global test conversion AJAX handler (works regardless of module status)
function redco_webp_ajax_test_conversion() {
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_test') && !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    $result = array(
        'success' => false,
        'message' => '',
        'capabilities' => array()
    );

    // Test WebP support
    $webp_support = function_exists('imagewebp') && (imagetypes() & IMG_WEBP);
    $result['capabilities']['webp_support'] = $webp_support;
    $result['capabilities']['gd_version'] = function_exists('gd_info') ? gd_info()['GD Version'] : 'Not available';
    $result['capabilities']['supported_formats'] = array();

    if (function_exists('imagetypes')) {
        $types = imagetypes();
        $result['capabilities']['supported_formats']['jpeg'] = (bool)($types & IMG_JPG);
        $result['capabilities']['supported_formats']['png'] = (bool)($types & IMG_PNG);
        $result['capabilities']['supported_formats']['gif'] = (bool)($types & IMG_GIF);
        $result['capabilities']['supported_formats']['webp'] = (bool)($types & IMG_WEBP);
    }

    if ($webp_support) {
        $result['success'] = true;
        $result['message'] = __('Server supports WebP conversion', 'redco-optimizer');
    } else {
        $result['message'] = __('Server does not support WebP conversion. Please contact your hosting provider.', 'redco-optimizer');
    }

    wp_send_json($result);
}

// Global bulk conversion AJAX handler
function redco_webp_ajax_bulk_convert() {
    // CRITICAL FIX: Add nonce verification for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_webp_bulk_convert')) {
        wp_send_json_error(array(
            'message' => 'Security verification failed. Please refresh the page and try again.',
            'error_code' => 'NONCE_FAILED'
        ));
        return;
    }

    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array(
            'message' => 'Insufficient permissions',
            'error_code' => 'INSUFFICIENT_PERMISSIONS'
        ));
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_error(array(
            'message' => 'Module not enabled',
            'error_code' => 'MODULE_DISABLED'
        ));
        return;
    }

    // CRITICAL FIX: Enhanced error handling for class loading
    try {
        // Get WebP module instance
        global $redco_webp_instance;
        if (!$redco_webp_instance) {
            if (!class_exists('Redco_Smart_WebP_Conversion')) {
                require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
            }

            if (class_exists('Redco_Smart_WebP_Conversion')) {
                $redco_webp_instance = new Redco_Smart_WebP_Conversion();
            } else {
                wp_send_json_error(array(
                    'message' => 'WebP conversion class not available',
                    'error_code' => 'CLASS_NOT_AVAILABLE'
                ));
                return;
            }
        }

        // Call the WebP class method
        $redco_webp_instance->ajax_bulk_convert();

    } catch (Exception $e) {
        wp_send_json_error(array(
            'message' => 'Error initializing WebP conversion: ' . $e->getMessage(),
            'error_code' => 'INITIALIZATION_ERROR'
        ));
    } catch (Error $e) {
        wp_send_json_error(array(
            'message' => 'Fatal error in WebP conversion: ' . $e->getMessage(),
            'error_code' => 'FATAL_ERROR'
        ));
    }
}

// Initialize Smart WebP Conversion module
function redco_init_smart_webp_conversion() {
    if (redco_is_module_enabled('smart-webp-conversion')) {
        global $redco_webp_instance;

        // Load the WebP conversion class
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        // Initialize WebP class
        try {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();
        } catch (Exception $e) {
            // Silently handle initialization errors
        } catch (Error $e) {
            // Silently handle fatal errors
        }

        // CRITICAL FIX: Enqueue WebP admin scripts and localize
        if (is_admin()) {
            add_action('admin_enqueue_scripts', 'redco_webp_enqueue_admin_scripts');
        }

        // Initialize WebP deletion hooks for comprehensive file cleanup
        redco_webp_init_deletion_hooks();
    }
}

// Enqueue WebP admin scripts with proper localization
function redco_webp_enqueue_admin_scripts($hook) {
    // More flexible hook checking for plugin pages
    $plugin_pages = array(
        'redco-optimizer',
        'redco_optimizer',
        'toplevel_page_redco-optimizer',
        'redco-optimizer_page_redco-optimizer-modules'
    );

    $is_plugin_page = false;
    foreach ($plugin_pages as $page) {
        if (strpos($hook, $page) !== false) {
            $is_plugin_page = true;
            break;
        }
    }

    if (!$is_plugin_page) {
        return;
    }



    // Enqueue WebP admin JavaScript
    wp_enqueue_script(
        'redco-webp-admin',
        REDCO_OPTIMIZER_PLUGIN_URL . 'modules/smart-webp-conversion/assets/js/admin.js',
        array('jquery'),
        REDCO_OPTIMIZER_VERSION,
        true
    );

    // CRITICAL FIX: Localize script with proper nonces and AJAX URL
    wp_localize_script('redco-webp-admin', 'redcoWebP', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonces' => array(
            'bulk_convert' => wp_create_nonce('redco_webp_bulk_convert'),
            'stats' => wp_create_nonce('redco_webp_stats'),
            'test' => wp_create_nonce('redco_webp_test')
        ),
        'strings' => array(
            'converting' => __('Converting...', 'redco-optimizer'),
            'completed' => __('Conversion completed!', 'redco-optimizer'),
            'error' => __('Conversion failed', 'redco-optimizer'),
            'noImages' => __('No images to convert', 'redco-optimizer')
        )
    ));


}

// Only register the init hook if it hasn't been registered already
if (!has_action('init', 'redco_init_smart_webp_conversion')) {
    add_action('init', 'redco_init_smart_webp_conversion', 10);
}

/**
 * WebP Deletion Handler - Comprehensive file cleanup for WebP converted attachments
 */

/**
 * Initialize WebP deletion hooks when module is enabled
 */
function redco_webp_init_deletion_hooks() {
    // Only register hooks if WebP module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        return;
    }

    // Hook into WordPress deletion process with multiple hooks for reliability
    add_action('before_delete_post', 'redco_webp_before_delete_attachment', 10, 2);
    add_action('delete_attachment', 'redco_webp_delete_attachment_handler', 10, 1);
    add_filter('wp_delete_attachment_files', 'redco_webp_delete_attachment_files_filter', 10, 3);
}

/**
 * Early detection and cleanup before WordPress starts deletion process
 * This runs before WordPress processes the deletion
 */
function redco_webp_before_delete_attachment($post_id, $post) {
    // Only process attachments
    if (!$post || $post->post_type !== 'attachment') {
        return;
    }

    // Check if this attachment has WebP conversion data
    $conversion_data = get_post_meta($post_id, '_webp_conversion_data', true);
    if (!$conversion_data || !is_array($conversion_data)) {
        return; // Not a WebP converted attachment
    }

    // Log the detection
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log("🗑️ WEBP DELETION: Detected WebP converted attachment {$post_id} scheduled for deletion");
    }

    // Store the conversion data in a transient for the deletion process
    // This ensures we have the data even if metadata gets deleted first
    set_transient("redco_webp_deletion_{$post_id}", $conversion_data, 300); // 5 minutes
}

/**
 * Main deletion handler - handles comprehensive cleanup
 */
function redco_webp_delete_attachment_handler($post_id) {
    // Get conversion data from transient or metadata
    $conversion_data = get_transient("redco_webp_deletion_{$post_id}");
    if (!$conversion_data) {
        $conversion_data = get_post_meta($post_id, '_webp_conversion_data', true);
    }

    // Clean up the transient
    delete_transient("redco_webp_deletion_{$post_id}");

    if (!$conversion_data || !is_array($conversion_data)) {
        return; // Not a WebP converted attachment
    }

    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log("🗑️ WEBP DELETION: Processing comprehensive cleanup for attachment {$post_id}");
    }

    // Perform comprehensive file cleanup
    redco_webp_comprehensive_file_cleanup($post_id, $conversion_data);
}

/**
 * File deletion filter - ensures all WebP files are included in deletion
 */
function redco_webp_delete_attachment_files_filter($should_delete, $attachment_id, $meta) {
    // Get conversion data
    $conversion_data = get_transient("redco_webp_deletion_{$attachment_id}");
    if (!$conversion_data) {
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
    }

    if (!$conversion_data || !is_array($conversion_data)) {
        return $should_delete; // Not a WebP converted attachment
    }

    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log("🗑️ WEBP DELETION: File deletion filter triggered for attachment {$attachment_id}");
    }

    // Let WordPress handle its normal deletion process
    // Our comprehensive cleanup will handle the WebP files
    return $should_delete;
}

/**
 * Comprehensive file cleanup for WebP converted attachments
 * Handles both NEW and LEGACY architectures
 */
function redco_webp_comprehensive_file_cleanup($attachment_id, $conversion_data) {
    $files_deleted = 0;
    $files_failed = 0;
    $cleanup_log = array();

    try {
        // Determine architecture type
        $is_new_architecture = isset($conversion_data['original_preserved']) && $conversion_data['original_preserved'];

        if (defined('WP_DEBUG') && WP_DEBUG) {
            $architecture = $is_new_architecture ? 'NEW' : 'LEGACY';
            error_log("🗑️ WEBP CLEANUP: Processing {$architecture} architecture for attachment {$attachment_id}");
        }

        // 1. Delete WebP main file
        if (isset($conversion_data['webp_path']) && !empty($conversion_data['webp_path'])) {
            if (file_exists($conversion_data['webp_path'])) {
                if (@unlink($conversion_data['webp_path'])) {
                    $files_deleted++;
                    $cleanup_log[] = "✅ Deleted WebP main file: " . basename($conversion_data['webp_path']);
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🗑️ WEBP CLEANUP: ✅ Deleted WebP main file: " . $conversion_data['webp_path']);
                    }
                } else {
                    $files_failed++;
                    $cleanup_log[] = "❌ Failed to delete WebP main file: " . basename($conversion_data['webp_path']);
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🗑️ WEBP CLEANUP: ❌ Failed to delete WebP main file: " . $conversion_data['webp_path']);
                    }
                }
            } else {
                $cleanup_log[] = "⚠️ WebP main file not found: " . basename($conversion_data['webp_path']);
            }
        }

        // 2. Delete WebP thumbnail files
        if (isset($conversion_data['webp_sizes']) && is_array($conversion_data['webp_sizes'])) {
            foreach ($conversion_data['webp_sizes'] as $size_name => $webp_size_path) {
                if (!empty($webp_size_path) && file_exists($webp_size_path)) {
                    if (@unlink($webp_size_path)) {
                        $files_deleted++;
                        $cleanup_log[] = "✅ Deleted WebP thumbnail ({$size_name}): " . basename($webp_size_path);
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🗑️ WEBP CLEANUP: ✅ Deleted WebP thumbnail ({$size_name}): {$webp_size_path}");
                        }
                    } else {
                        $files_failed++;
                        $cleanup_log[] = "❌ Failed to delete WebP thumbnail ({$size_name}): " . basename($webp_size_path);
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🗑️ WEBP CLEANUP: ❌ Failed to delete WebP thumbnail ({$size_name}): {$webp_size_path}");
                        }
                    }
                } else if (!empty($webp_size_path)) {
                    $cleanup_log[] = "⚠️ WebP thumbnail not found ({$size_name}): " . basename($webp_size_path);
                }
            }
        }

        // 3. For NEW architecture, also delete original files (they're hidden from WordPress)
        if ($is_new_architecture) {
            // Delete original main file with comprehensive path reconstruction
            if (isset($conversion_data['file_path']) && !empty($conversion_data['file_path'])) {
                $original_file_path = $conversion_data['file_path'];

                // CRITICAL FIX: Comprehensive path reconstruction for malformed paths
                if (!file_exists($original_file_path)) {
                    $found_original = false;

                    // Method 1: Fix common path corruption issues
                    $fixed_path = $original_file_path;

                    // Fix missing drive letter backslashes (D:xampp -> D:\xampp)
                    if (preg_match('/^([A-Z]):([^\\\\])/', $fixed_path, $matches)) {
                        $fixed_path = $matches[1] . ':\\' . $matches[2] . substr($fixed_path, 3);
                    }

                    // Normalize path separators
                    $fixed_path = wp_normalize_path($fixed_path);

                    if (file_exists($fixed_path)) {
                        $original_file_path = $fixed_path;
                        $found_original = true;
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🗑️ WEBP CLEANUP: 🔧 Fixed corrupted path: {$original_file_path}");
                        }
                    }

                    // Method 2: Reconstruct from WebP path if fix didn't work
                    if (!$found_original && isset($conversion_data['webp_path']) && file_exists($conversion_data['webp_path'])) {
                        $webp_path = $conversion_data['webp_path'];
                        $webp_dir = dirname($webp_path);
                        $webp_filename = pathinfo($webp_path, PATHINFO_FILENAME);

                        // Try different extensions for the original file
                        $possible_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                        foreach ($possible_extensions as $ext) {
                            $reconstructed_path = $webp_dir . '/' . $webp_filename . '.' . $ext;
                            if (file_exists($reconstructed_path)) {
                                $original_file_path = $reconstructed_path;
                                $found_original = true;
                                if (defined('WP_DEBUG') && WP_DEBUG) {
                                    error_log("🗑️ WEBP CLEANUP: 🔧 Reconstructed from WebP path: {$original_file_path}");
                                }
                                break;
                            }
                        }
                    }

                    // Method 3: Use attachment title to find file if other methods failed
                    if (!$found_original && isset($conversion_data['attachment_title'])) {
                        $upload_dir = wp_upload_dir();
                        $title_slug = sanitize_file_name($conversion_data['attachment_title']);

                        // Try to find file by title in current upload directory
                        $possible_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                        foreach ($possible_extensions as $ext) {
                            $title_based_path = $upload_dir['path'] . '/' . $title_slug . '.' . $ext;
                            if (file_exists($title_based_path)) {
                                $original_file_path = $title_based_path;
                                $found_original = true;
                                if (defined('WP_DEBUG') && WP_DEBUG) {
                                    error_log("🗑️ WEBP CLEANUP: 🔧 Found by title: {$original_file_path}");
                                }
                                break;
                            }
                        }
                    }
                }

                if (file_exists($original_file_path)) {
                    if (@unlink($original_file_path)) {
                        $files_deleted++;
                        $cleanup_log[] = "✅ Deleted original main file: " . basename($original_file_path);
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🗑️ WEBP CLEANUP: ✅ Deleted original main file: " . $original_file_path);
                        }
                    } else {
                        $files_failed++;
                        $cleanup_log[] = "❌ Failed to delete original main file: " . basename($original_file_path);
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🗑️ WEBP CLEANUP: ❌ Failed to delete original main file: " . $original_file_path);
                        }
                    }
                } else {
                    $cleanup_log[] = "⚠️ Original main file not found: " . basename($conversion_data['file_path']);
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🗑️ WEBP CLEANUP: ⚠️ Original main file not found even after reconstruction: " . $conversion_data['file_path']);
                    }
                }
            }

            // Delete original thumbnail files with path reconstruction
            if (isset($conversion_data['original_metadata']['sizes']) && is_array($conversion_data['original_metadata']['sizes'])) {
                // Use the reconstructed original file path if available, otherwise use stored path
                $original_dir = isset($original_file_path) ? dirname($original_file_path) : dirname($conversion_data['file_path']);

                foreach ($conversion_data['original_metadata']['sizes'] as $size_name => $size_data) {
                    if (isset($size_data['file']) && !empty($size_data['file'])) {
                        $thumbnail_path = $original_dir . '/' . $size_data['file'];

                        // CRITICAL FIX: If thumbnail doesn't exist, try multiple reconstruction methods
                        if (!file_exists($thumbnail_path)) {
                            $found_thumbnail = false;

                            // Method 1: Try to reconstruct from WebP thumbnails
                            if (isset($conversion_data['webp_sizes'][$size_name])) {
                                $webp_thumb_path = $conversion_data['webp_sizes'][$size_name];
                                if (file_exists($webp_thumb_path)) {
                                    $webp_thumb_dir = dirname($webp_thumb_path);
                                    $webp_thumb_filename = pathinfo($webp_thumb_path, PATHINFO_FILENAME);

                                    // Try different extensions for the original thumbnail
                                    $possible_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                                    foreach ($possible_extensions as $ext) {
                                        $reconstructed_thumb_path = $webp_thumb_dir . '/' . $webp_thumb_filename . '.' . $ext;
                                        if (file_exists($reconstructed_thumb_path)) {
                                            $thumbnail_path = $reconstructed_thumb_path;
                                            $found_thumbnail = true;
                                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                                error_log("🗑️ WEBP CLEANUP: 🔧 Reconstructed thumbnail from WebP ({$size_name}): {$thumbnail_path}");
                                            }
                                            break;
                                        }
                                    }
                                }
                            }

                            // Method 2: If still not found, try to reconstruct from original file path
                            if (!$found_thumbnail && isset($original_file_path)) {
                                $original_dir = dirname($original_file_path);
                                $original_filename = pathinfo($original_file_path, PATHINFO_FILENAME);
                                $original_extension = pathinfo($original_file_path, PATHINFO_EXTENSION);

                                // Try to find thumbnail with same pattern as original
                                $thumbnail_pattern = $original_filename . '-' . $size_data['width'] . 'x' . $size_data['height'] . '.' . $original_extension;
                                $reconstructed_thumb_path = $original_dir . '/' . $thumbnail_pattern;

                                if (file_exists($reconstructed_thumb_path)) {
                                    $thumbnail_path = $reconstructed_thumb_path;
                                    $found_thumbnail = true;
                                    if (defined('WP_DEBUG') && WP_DEBUG) {
                                        error_log("🗑️ WEBP CLEANUP: 🔧 Reconstructed thumbnail from original pattern ({$size_name}): {$thumbnail_path}");
                                    }
                                }
                            }

                            // Method 3: If still not found, scan directory for matching files
                            if (!$found_thumbnail) {
                                $scan_dir = isset($original_file_path) ? dirname($original_file_path) : $original_dir;
                                if (is_dir($scan_dir)) {
                                    $files_in_dir = scandir($scan_dir);
                                    $target_filename = $size_data['file'];

                                    foreach ($files_in_dir as $file) {
                                        if ($file === $target_filename && file_exists($scan_dir . '/' . $file)) {
                                            $thumbnail_path = $scan_dir . '/' . $file;
                                            $found_thumbnail = true;
                                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                                error_log("🗑️ WEBP CLEANUP: 🔧 Found thumbnail by directory scan ({$size_name}): {$thumbnail_path}");
                                            }
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                        if (file_exists($thumbnail_path)) {
                            if (@unlink($thumbnail_path)) {
                                $files_deleted++;
                                $cleanup_log[] = "✅ Deleted original thumbnail ({$size_name}): " . basename($thumbnail_path);
                                if (defined('WP_DEBUG') && WP_DEBUG) {
                                    error_log("🗑️ WEBP CLEANUP: ✅ Deleted original thumbnail ({$size_name}): {$thumbnail_path}");
                                }
                            } else {
                                $files_failed++;
                                $cleanup_log[] = "❌ Failed to delete original thumbnail ({$size_name}): " . basename($thumbnail_path);
                                if (defined('WP_DEBUG') && WP_DEBUG) {
                                    error_log("🗑️ WEBP CLEANUP: ❌ Failed to delete original thumbnail ({$size_name}): {$thumbnail_path}");
                                }
                            }
                        } else {
                            $cleanup_log[] = "⚠️ Original thumbnail not found ({$size_name}): " . basename($thumbnail_path);
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🗑️ WEBP CLEANUP: ⚠️ Original thumbnail not found even after reconstruction ({$size_name}): {$thumbnail_path}");
                            }
                        }
                    }
                }
            }
        }

        // 4. Clean up conversion metadata
        if (delete_post_meta($attachment_id, '_webp_conversion_data')) {
            $cleanup_log[] = "✅ Cleaned up WebP conversion metadata";
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🗑️ WEBP CLEANUP: ✅ Cleaned up WebP conversion metadata for attachment {$attachment_id}");
            }
        } else {
            $cleanup_log[] = "⚠️ WebP conversion metadata not found or already cleaned";
        }

        // Log final results
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🗑️ WEBP CLEANUP: Completed for attachment {$attachment_id} - Files deleted: {$files_deleted}, Failed: {$files_failed}");
            error_log("🗑️ WEBP CLEANUP: Details: " . implode(' | ', $cleanup_log));
        }

    } catch (Exception $e) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🗑️ WEBP CLEANUP: Exception during cleanup for attachment {$attachment_id}: " . $e->getMessage());
        }
    } catch (Error $e) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🗑️ WEBP CLEANUP: Fatal error during cleanup for attachment {$attachment_id}: " . $e->getMessage());
        }
    }

    return array(
        'files_deleted' => $files_deleted,
        'files_failed' => $files_failed,
        'cleanup_log' => $cleanup_log
    );
}









/**
 * UNINSTALL RECOVERY: WebP uninstall hook handler
 * This function is called during plugin uninstall to restore all WebP conversions
 */
function redco_webp_uninstall_recovery() {
    // Only proceed if the WebP class exists
    if (!class_exists('Redco_Smart_WebP_Conversion')) {
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
    }

    if (!class_exists('Redco_Smart_WebP_Conversion')) {
        return; // Cannot proceed without the class
    }

    try {
        // Perform bulk restoration of all WebP conversions
        $results = Redco_Smart_WebP_Conversion::bulk_restore_all_conversions();

        // Log the results for debugging (if WP_DEBUG is enabled)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔄 WEBP UNINSTALL RECOVERY RESULTS: ' . print_r($results, true));
        }

        // Validate the recovery process
        $validation = Redco_Smart_WebP_Conversion::validate_recovery();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔍 WEBP RECOVERY VALIDATION: ' . print_r($validation, true));
        }

        // If validation fails, log the issues but don't stop the uninstall process
        if (!$validation['success']) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('⚠️ WEBP RECOVERY VALIDATION FAILED: ' . implode(', ', $validation['issues']));
            }
        }

    } catch (Exception $e) {
        // Log the error but don't stop the uninstall process
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('❌ WEBP UNINSTALL RECOVERY ERROR: ' . $e->getMessage());
        }
    }
}

// Register the WebP uninstall recovery hook
add_action('redco_optimizer_uninstall', 'redco_webp_uninstall_recovery', 10);

/**
 * DEACTIVATION WARNING: Check for WebP conversions during plugin deactivation
 */
function redco_webp_deactivation_warning() {
    global $wpdb;

    // Check if there are any WebP conversions
    $webp_count = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->postmeta}
        WHERE meta_key = '_webp_conversion_data'
        AND (meta_value LIKE '%s:9:\"converted\";b:1%' OR meta_value LIKE '%\"converted\":true%')
    ");

    if ($webp_count > 0) {
        // Store a transient to show warning on next admin page load
        set_transient('redco_webp_deactivation_warning', array(
            'webp_count' => $webp_count,
            'timestamp' => time()
        ), 300); // Show for 5 minutes
    }
}

/**
 * Display deactivation warning in admin
 */
function redco_webp_show_deactivation_warning() {
    $warning_data = get_transient('redco_webp_deactivation_warning');

    if ($warning_data && is_array($warning_data)) {
        $webp_count = $warning_data['webp_count'];

        echo '<div class="notice notice-warning is-dismissible">';
        echo '<h3>⚠️ Redco Optimizer WebP Conversion Warning</h3>';
        echo '<p><strong>Important:</strong> You have ' . $webp_count . ' images that were converted to WebP format.</p>';
        echo '<p>If you uninstall this plugin, your media library may show broken images because WordPress will still reference the WebP files.</p>';
        echo '<p><strong>Recommended actions:</strong></p>';
        echo '<ul>';
        echo '<li>• <strong>Before uninstalling:</strong> Use the "Reset All Conversions" feature in the WebP module to restore original images</li>';
        echo '<li>• <strong>If already uninstalled:</strong> Use the <a href="' . site_url('/webp-recovery-tool.php') . '" target="_blank">WebP Recovery Tool</a> to restore your media library</li>';
        echo '</ul>';
        echo '<p><em>This warning will disappear automatically in a few minutes.</em></p>';
        echo '</div>';

        // Clear the transient after showing
        delete_transient('redco_webp_deactivation_warning');
    }
}

// Register deactivation warning hooks
add_action('redco_optimizer_deactivate', 'redco_webp_deactivation_warning', 5);
add_action('admin_notices', 'redco_webp_show_deactivation_warning');

// AJAX handler for getting convertible images count
function redco_webp_ajax_get_convertible_count() {
    // Security check - verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_webp_bulk_convert')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_success(array(
            'convertible_count' => 0,
            'has_convertible_images' => false,
            'message' => 'Module not enabled'
        ));
        return;
    }

    try {
        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();
        $convertible_count = $webp_instance->get_convertible_images_count();

        $response_data = array(
            'convertible_count' => $convertible_count,
            'has_convertible_images' => $convertible_count > 0,
            'message' => $convertible_count > 0 ?
                sprintf('%d images ready for conversion', $convertible_count) :
                'No images available for conversion'
        );

        wp_send_json_success($response_data);

    } catch (Exception $e) {
        wp_send_json_error('Failed to get convertible images count: ' . $e->getMessage());
    }
}

// AJAX handler for getting processable images
function redco_webp_ajax_get_processable_images() {
    // Security check
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_webp_bulk_convert')) {
        wp_send_json_error(array('message' => 'Security check failed - invalid nonce'));
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'Security check failed - insufficient permissions'));
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_error(array('message' => 'Module not enabled'));
        return;
    }

    try {
        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            $class_file = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
            if (!file_exists($class_file)) {
                throw new Exception('WebP class file not found: ' . $class_file);
            }
            require_once $class_file;
        }

        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            throw new Exception('WebP class could not be loaded');
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();

        // Check if method exists
        if (!method_exists($webp_instance, 'get_all_processable_images')) {
            throw new Exception('Method get_all_processable_images does not exist');
        }

        $processable_images = $webp_instance->get_all_processable_images();

        wp_send_json_success(array(
            'total_images' => count($processable_images),
            'image_ids' => array_column($processable_images, 'ID'),
            'message' => 'Found ' . count($processable_images) . ' processable images'
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to get processable images: ' . $e->getMessage());
    } catch (Error $e) {
        wp_send_json_error('Fatal error getting processable images: ' . $e->getMessage());
    }
}

// AJAX handler for getting recent conversions
function redco_webp_ajax_get_recent_conversions() {
    // CRITICAL FIX: More flexible nonce verification for recent conversions
    $nonce_verified = false;
    if (isset($_POST['nonce'])) {
        $nonce_verified = wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') ||
                         wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce');
    }

    if (!$nonce_verified) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_success(array(
            'conversions' => array(),
            'total_count' => 0,
            'has_more' => false
        ));
        return;
    }

    try {
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
        $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;

        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();
        $conversions = $webp_instance->get_recent_conversions($limit, $offset);
        $total_count = $webp_instance->get_conversions_count();

        wp_send_json_success(array(
            'conversions' => $conversions,
            'total_count' => $total_count,
            'has_more' => ($offset + $limit) < $total_count,
            'current_offset' => $offset,
            'current_limit' => $limit
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to get recent conversions: ' . $e->getMessage());
    }
}

// AJAX handler for refreshing stats with real-time database queries
function redco_webp_ajax_refresh_stats() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_error('Module not enabled');
        return;
    }

    try {
        global $wpdb;

        // Real-time database queries for accurate stats

        // 1. Get total convertible images (original formats only) - NONE LEFT, ALL CONVERTED
        $total_images = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
        ");

        // 2. Get converted WebP images using serialized data format
        $converted_webp_images = $wpdb->get_results("
            SELECT pm.post_id, pm.meta_value
            FROM {$wpdb->postmeta} pm
            WHERE pm.meta_key = '_webp_conversion_data'
            AND (pm.meta_value LIKE '%s:9:\"converted\";b:1%' OR pm.meta_value LIKE '%\"converted\":true%')
        ");

        // 3. Count WebP images in posts table
        $webp_images_count = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_mime_type = 'image/webp'
        ");

        $converted_images = 0;
        $total_original_size = 0;
        $total_webp_size = 0;
        $total_savings = 0;
        $savings_percentages = array();

        // Process WebP conversion data to calculate savings
        foreach ($converted_webp_images as $row) {
            $conversion_data = maybe_unserialize($row->meta_value);
            if (is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted']) {
                $converted_images++;

                if (isset($conversion_data['original_size']) && isset($conversion_data['webp_size'])) {
                    $original_size = intval($conversion_data['original_size']);
                    $webp_size = intval($conversion_data['webp_size']);

                    $total_original_size += $original_size;
                    $total_webp_size += $webp_size;

                    $savings = $original_size - $webp_size;
                    $total_savings += $savings;

                    // Calculate individual savings percentage
                    if ($original_size > 0) {
                        $savings_percentage = round(($savings / $original_size) * 100, 1);
                        $savings_percentages[] = $savings_percentage;
                    }
                }
            }
        }

        // Total images is the count of current original format images (0 if all converted)

        // Calculate averages and percentages
        $unconverted_images = max(0, $total_images - $converted_images);

        // Calculate conversion rate: converted / (total original + converted) * 100
        $total_all_images = $total_images + $converted_images; // Total original + converted images

        if ($total_all_images > 0) {
            $conversion_percentage = round(($converted_images / $total_all_images) * 100, 1);
        } else {
            $conversion_percentage = 0;
        }
        $avg_savings_percentage = count($savings_percentages) > 0 ? round(array_sum($savings_percentages) / count($savings_percentages), 1) : 0;

        // 3. Get convertible images count for button state (exclude already WebP images)
        $convertible_count = $wpdb->get_var("
            SELECT COUNT(DISTINCT p.ID)
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            AND (pm.meta_value IS NULL OR pm.meta_value NOT LIKE '%\"converted\":true%')
        ");

        // Get recent conversions with actual timestamps from when the meta was created
        $recent_conversions_raw = $wpdb->get_results("
            SELECT pm.post_id, pm.meta_value, pm.meta_id, p.post_modified
            FROM {$wpdb->postmeta} pm
            INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = '_webp_conversion_data'
            AND (pm.meta_value LIKE '%s:9:\"converted\";b:1%' OR pm.meta_value LIKE '%\"converted\":true%')
            ORDER BY pm.meta_id DESC
            LIMIT 10
        ");

        $recent_conversions = array();
        $conversions_with_timestamps = array();

        // First pass: collect all conversions with their timestamps
        foreach ($recent_conversions_raw as $row) {
            $conversion_data = maybe_unserialize($row->meta_value);
            if (is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted']) {
                // Format for display to match what JavaScript expects
                $post_title = get_the_title($row->post_id) ?: 'Unknown Image';
                $original_size = isset($conversion_data['original_size']) ? $conversion_data['original_size'] : 0;
                $webp_size = isset($conversion_data['webp_size']) ? $conversion_data['webp_size'] : 0;
                $savings_percentage = $original_size > 0 ? round((($original_size - $webp_size) / $original_size) * 100, 2) : 0;

                // Use timestamp from conversion data if available, otherwise use post_modified
                $timestamp = null;
                if (isset($conversion_data['timestamp'])) {
                    $timestamp = $conversion_data['timestamp'];
                } elseif (isset($conversion_data['converted_at'])) {
                    $timestamp = $conversion_data['converted_at'];
                } else {
                    // Use the post's last modified date as the conversion timestamp
                    $timestamp = strtotime($row->post_modified);
                }

                $formatted_conversion = array(
                    'title' => $post_title,
                    'formatted_original_size' => size_format($original_size),
                    'formatted_webp_size' => size_format($webp_size),
                    'savings_percentage' => $savings_percentage,
                    'formatted_date' => human_time_diff($timestamp, current_time('timestamp')) . ' ago',
                    'timestamp' => $timestamp,
                    'meta_id' => $row->meta_id
                );

                $conversions_with_timestamps[] = $formatted_conversion;
            }
        }

        // Sort by timestamp (most recent first) and then by meta_id as fallback
        usort($conversions_with_timestamps, function($a, $b) {
            if ($a['timestamp'] == $b['timestamp']) {
                return $b['meta_id'] - $a['meta_id']; // Higher meta_id first
            }
            return $b['timestamp'] - $a['timestamp']; // More recent timestamp first
        });

        // Take only the first 10 (most recent) and remove the sorting fields
        foreach (array_slice($conversions_with_timestamps, 0, 10) as $conversion) {
            unset($conversion['timestamp'], $conversion['meta_id']);
            $recent_conversions[] = $conversion;
        }



        // Prepare comprehensive stats response
        $stats = array(
            'total_images' => intval($total_images),
            'converted_images' => intval($converted_images),
            'unconverted_images' => intval($unconverted_images),
            'convertible_count' => intval($convertible_count),
            'conversion_percentage' => floatval($conversion_percentage),
            'total_original_size' => intval($total_original_size),
            'total_webp_size' => intval($total_webp_size),
            'total_savings' => intval($total_savings),
            'savings_percentage' => floatval($avg_savings_percentage),
            'server_support' => function_exists('imagewebp') && (imagetypes() & IMG_WEBP),
            'browser_support' => false // Will be set by JavaScript
        );



        wp_send_json_success(array(
            'stats' => $stats,
            'recent_conversions' => $recent_conversions,
            'message' => 'Statistics refreshed successfully from database',
            'timestamp' => current_time('mysql')
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to refresh stats: ' . $e->getMessage());
    } catch (Error $e) {
        wp_send_json_error('Fatal error refreshing stats: ' . $e->getMessage());
    }
}

// AJAX handler for scanning images
function redco_webp_ajax_scan_images() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_error('Module not enabled');
        return;
    }

    try {
        global $wpdb;

        // Get all image attachments (including WebP)
        $all_images = $wpdb->get_results("
            SELECT p.ID, p.post_title, p.post_mime_type
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp')
            ORDER BY p.ID DESC
        ");

        // Get converted images
        $converted_images = $wpdb->get_results("
            SELECT DISTINCT pm.post_id
            FROM {$wpdb->postmeta} pm
            INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%\"converted\":true%'
            AND p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp')
        ");

        $converted_ids = array_column($converted_images, 'post_id');
        $unconverted_count = 0;
        $total_size = 0;
        $scan_results = array();

        // Analyze each image
        foreach ($all_images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            $is_converted = in_array($image->ID, $converted_ids);

            if ($file_path && file_exists($file_path)) {
                $file_size = filesize($file_path);
                $total_size += $file_size;

                if (!$is_converted) {
                    $unconverted_count++;
                }

                $scan_results[] = array(
                    'id' => $image->ID,
                    'title' => $image->post_title,
                    'mime_type' => $image->post_mime_type,
                    'file_size' => $file_size,
                    'is_converted' => $is_converted,
                    'file_exists' => true
                );
            }
        }

        // Calculate potential savings
        $estimated_savings = $unconverted_count * 0.3; // 30% average savings
        $estimated_size_savings = $total_size * 0.3;

        wp_send_json_success(array(
            'total_images' => count($all_images),
            'converted_images' => count($converted_images),
            'unconverted_images' => $unconverted_count,
            'total_size' => $total_size,
            'estimated_savings_percent' => 30,
            'estimated_size_savings' => $estimated_size_savings,
            'scan_results' => array_slice($scan_results, 0, 20), // Limit to 20 for performance
            'message' => "Scan complete: {$unconverted_count} images ready for WebP conversion"
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to scan images: ' . $e->getMessage());
    }
}

// AJAX handler for analyzing WebP potential
function redco_webp_ajax_analyze_webp_potential() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    try {
        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();
        $processable_images = $webp_instance->get_all_processable_images();

        // Analyze file sizes and potential savings
        $total_original_size = 0;
        $analysis_details = array();

        foreach (array_slice($processable_images, 0, 10) as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            if ($file_path && file_exists($file_path)) {
                $file_size = filesize($file_path);
                $total_original_size += $file_size;

                $analysis_details[] = array(
                    'id' => $image->ID,
                    'title' => $image->post_title,
                    'mime_type' => $image->post_mime_type,
                    'file_size' => $file_size,
                    'estimated_webp_size' => $file_size * 0.7, // 30% savings
                    'estimated_savings' => $file_size * 0.3
                );
            }
        }

        $estimated_total_savings = $total_original_size * 0.3;

        wp_send_json_success(array(
            'processable_count' => count($processable_images),
            'total_original_size' => $total_original_size,
            'estimated_total_savings' => $estimated_total_savings,
            'savings_percentage' => 30,
            'analysis_details' => $analysis_details,
            'server_support' => function_exists('imagewebp') && (imagetypes() & IMG_WEBP),
            'message' => count($processable_images) . ' images analyzed for WebP potential'
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to analyze WebP potential: ' . $e->getMessage());
    }
}



// AJAX handler for database inspection
function redco_webp_ajax_inspect_database() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    try {
        global $wpdb;

        // Get all attachments
        $all_attachments = $wpdb->get_results("
            SELECT ID, post_title, post_mime_type, post_status
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            ORDER BY ID DESC
        ");

        // Get image attachments only
        $image_attachments = $wpdb->get_results("
            SELECT ID, post_title, post_mime_type, post_status
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_mime_type LIKE 'image/%'
            ORDER BY ID DESC
        ");

        // Get all unique MIME types for debugging
        $all_mime_types = $wpdb->get_results("
            SELECT DISTINCT post_mime_type, COUNT(*) as count
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            GROUP BY post_mime_type
            ORDER BY count DESC
        ");

        // Get all WebP conversion meta
        $webp_meta = $wpdb->get_results("
            SELECT post_id, meta_value
            FROM {$wpdb->postmeta}
            WHERE meta_key = '_webp_conversion_data'
        ");

        // Get converted WebP meta
        $converted_meta = $wpdb->get_results("
            SELECT post_id, meta_value
            FROM {$wpdb->postmeta}
            WHERE meta_key = '_webp_conversion_data'
            AND meta_value LIKE '%\"converted\":true%'
        ");

        wp_send_json_success(array(
            'total_attachments' => count($all_attachments),
            'image_attachments' => count($image_attachments),
            'webp_meta_records' => count($webp_meta),
            'converted_meta_records' => count($converted_meta),
            'sample_attachments' => array_slice($all_attachments, 0, 5),
            'sample_image_attachments' => array_slice($image_attachments, 0, 5),
            'sample_webp_meta' => array_slice($webp_meta, 0, 3),
            'sample_converted_meta' => array_slice($converted_meta, 0, 3),
            'all_mime_types' => $all_mime_types
        ));

    } catch (Exception $e) {
        wp_send_json_error('Database inspection failed: ' . $e->getMessage());
    }
}

// Register AJAX handlers
if (is_admin()) {
    add_action('wp_ajax_redco_webp_bulk_convert', 'redco_webp_ajax_bulk_convert');
    add_action('wp_ajax_redco_webp_get_stats', 'redco_webp_ajax_get_stats');
    add_action('wp_ajax_redco_webp_test_conversion', 'redco_webp_ajax_test_conversion');
    add_action('wp_ajax_redco_webp_get_convertible_count', 'redco_webp_ajax_get_convertible_count');
    add_action('wp_ajax_redco_webp_get_processable_images', 'redco_webp_ajax_get_processable_images');
    add_action('wp_ajax_redco_webp_get_recent_conversions', 'redco_webp_ajax_get_recent_conversions');

    // NEW AJAX handlers for new functionality
    add_action('wp_ajax_redco_webp_refresh_stats', 'redco_webp_ajax_refresh_stats');
    add_action('wp_ajax_redco_webp_scan_images', 'redco_webp_ajax_scan_images');
    add_action('wp_ajax_redco_webp_fix_metadata', 'redco_webp_ajax_fix_metadata');
}

// AJAX handler for fixing WebP metadata
function redco_webp_ajax_fix_metadata() {
    // Get WebP module instance
    global $redco_webp_instance;
    if (!$redco_webp_instance) {
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        if (class_exists('Redco_Smart_WebP_Conversion')) {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();
        } else {
            wp_send_json_error('WebP conversion class not available');
            return;
        }
    }

    // Call the WebP class method
    $redco_webp_instance->ajax_fix_webp_metadata();
}

// AJAX handler for resetting WebP conversions with metadata restoration and progress tracking
function redco_webp_ajax_reset_conversions() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    check_ajax_referer('redco_webp_stats', 'nonce');

    try {
        // Get or generate session ID for progress tracking
        $session_id = isset($_POST['session_id']) ? sanitize_text_field($_POST['session_id']) : 'webp_reset_' . time();

        // Initialize progress tracking
        if (class_exists('Redco_Progress_Tracker')) {
            Redco_Progress_Tracker::start_session($session_id, 100);
            Redco_Progress_Tracker::update_progress($session_id, array(
                'percentage' => 5,
                'current_operation' => 'Initializing Reset Process',
                'operation_details' => 'Preparing to reset WebP conversions and restore metadata...'
            ));
        }

        // Use the enhanced bulk restoration method
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            if (class_exists('Redco_Progress_Tracker')) {
                Redco_Progress_Tracker::set_error($session_id, 'WebP conversion class not available');
            }
            wp_send_json_error('WebP conversion class not available');
            return;
        }

        // Update progress: Clearing cache
        if (class_exists('Redco_Progress_Tracker')) {
            Redco_Progress_Tracker::update_progress($session_id, array(
                'percentage' => 15,
                'current_operation' => 'Clearing Cache',
                'operation_details' => 'Clearing WebP-related cache and transients...'
            ));
        }

        // Clear any cached WebP data before reset
        if (class_exists('Redco_Advanced_Cache')) {
            Redco_Advanced_Cache::delete('webp_stats_v2', Redco_Advanced_Cache::GROUP_STATS);
        }

        // Update progress: Starting restoration
        if (class_exists('Redco_Progress_Tracker')) {
            Redco_Progress_Tracker::update_progress($session_id, array(
                'percentage' => 25,
                'current_operation' => 'Starting Restoration Process',
                'operation_details' => 'Beginning bulk restoration of all WebP conversions...'
            ));
        }

        // Perform bulk restoration with progress tracking
        $results = Redco_Smart_WebP_Conversion::bulk_restore_all_conversions($session_id);

        if ($results['success']) {
            // Complete progress tracking
            if (class_exists('Redco_Progress_Tracker')) {
                Redco_Progress_Tracker::complete_session($session_id, 'WebP conversions reset successfully', array(
                    'images_restored' => $results['images_restored'],
                    'metadata_restored' => $results['metadata_restored'],
                    'files_deleted' => $results['webp_files_removed'],
                    'records_cleared' => $results['database_entries_cleaned']
                ));
            }

            wp_send_json_success(array(
                'message' => 'WebP conversions reset and metadata restored successfully',
                'images_restored' => $results['images_restored'],
                'metadata_restored' => $results['metadata_restored'],
                'files_deleted' => $results['webp_files_removed'],
                'records_cleared' => $results['database_entries_cleaned'],
                'session_id' => $session_id,
                'log_cleared' => true
            ));
        } else {
            // Set error in progress tracking
            if (class_exists('Redco_Progress_Tracker')) {
                Redco_Progress_Tracker::set_error($session_id, 'Reset failed: ' . implode(', ', $results['errors']));
            }
            wp_send_json_error('Reset failed: ' . implode(', ', $results['errors']));
        }

    } catch (Exception $e) {
        // Set error in progress tracking
        if (class_exists('Redco_Progress_Tracker') && isset($session_id)) {
            Redco_Progress_Tracker::set_error($session_id, 'Reset failed: ' . $e->getMessage());
        }
        wp_send_json_error('Reset failed: ' . $e->getMessage());
    }
}

/**
 * AJAX handler for debugging metadata restoration
 */
function redco_webp_ajax_debug_metadata() {
    // Security checks
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    if (!check_ajax_referer('redco_webp_nonce', 'nonce', false)) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    try {
        // Enable debug logging temporarily
        $original_debug = defined('WP_DEBUG') ? WP_DEBUG : false;
        if (!defined('WP_DEBUG')) {
            define('WP_DEBUG', true);
        }

        // Run debug metadata restoration
        $processed_count = Redco_Smart_WebP_Conversion::debug_metadata_restoration();

        wp_send_json_success(array(
            'message' => 'Debug metadata restoration completed',
            'processed_attachments' => $processed_count,
            'debug_enabled' => true,
            'check_logs' => 'Check error logs for detailed debugging information'
        ));

    } catch (Exception $e) {
        wp_send_json_error('Debug failed: ' . $e->getMessage());
    }
}

// Register AJAX handlers
add_action('wp_ajax_redco_webp_debug_metadata', 'redco_webp_ajax_debug_metadata');
add_action('wp_ajax_redco_webp_reset_conversions', 'redco_webp_ajax_reset_conversions');
add_action('wp_ajax_redco_webp_analyze_webp_potential', 'redco_webp_ajax_analyze_webp_potential');
add_action('wp_ajax_redco_webp_inspect_database', 'redco_webp_ajax_inspect_database');
add_action('wp_ajax_redco_webp_get_convertible_count', 'redco_webp_ajax_get_convertible_count');
add_action('wp_ajax_redco_webp_get_processable_images', 'redco_webp_ajax_get_processable_images');
add_action('wp_ajax_redco_webp_get_recent_conversions', 'redco_webp_ajax_get_recent_conversions');
add_action('wp_ajax_redco_webp_refresh_stats', 'redco_webp_ajax_refresh_stats');
add_action('wp_ajax_redco_webp_scan_images', 'redco_webp_ajax_scan_images');
