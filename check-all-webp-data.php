<?php
/**
 * Check all WebP related data
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== All WebP Related Data ===\n\n";

global $wpdb;

// 1. Check for any WebP conversion metadata
echo "1. Checking for WebP conversion metadata:\n";
$webp_meta = $wpdb->get_results("
    SELECT post_id, meta_value
    FROM {$wpdb->postmeta}
    WHERE meta_key = '_webp_conversion_data'
    LIMIT 10
");

if (empty($webp_meta)) {
    echo "   ❌ No _webp_conversion_data found\n";
} else {
    echo "   ✅ Found " . count($webp_meta) . " records with _webp_conversion_data\n";
    foreach ($webp_meta as $meta) {
        echo "      - Post ID {$meta->post_id}: " . substr($meta->meta_value, 0, 100) . "...\n";
    }
}

// 2. Check for WebP MIME type attachments
echo "\n2. Checking for WebP MIME type attachments:\n";
$webp_attachments = $wpdb->get_results("
    SELECT ID, post_title, post_mime_type
    FROM {$wpdb->posts}
    WHERE post_type = 'attachment'
    AND post_mime_type = 'image/webp'
    LIMIT 10
");

if (empty($webp_attachments)) {
    echo "   ❌ No image/webp attachments found\n";
} else {
    echo "   ✅ Found " . count($webp_attachments) . " WebP attachments\n";
    foreach ($webp_attachments as $attachment) {
        echo "      - ID {$attachment->ID}: {$attachment->post_title}\n";
    }
}

// 3. Check for any image attachments
echo "\n3. Checking for any image attachments:\n";
$image_attachments = $wpdb->get_results("
    SELECT ID, post_title, post_mime_type
    FROM {$wpdb->posts}
    WHERE post_type = 'attachment'
    AND post_mime_type LIKE 'image/%'
    ORDER BY ID DESC
    LIMIT 10
");

if (empty($image_attachments)) {
    echo "   ❌ No image attachments found\n";
} else {
    echo "   ✅ Found " . count($image_attachments) . " image attachments\n";
    foreach ($image_attachments as $attachment) {
        echo "      - ID {$attachment->ID}: {$attachment->post_title} ({$attachment->post_mime_type})\n";
        
        // Check if this has WebP conversion data
        $conversion_data = get_post_meta($attachment->ID, '_webp_conversion_data', true);
        if ($conversion_data) {
            echo "        📊 Has WebP conversion data\n";
            
            // Parse the data
            if (is_array($conversion_data)) {
                echo "        📁 WebP path: " . (isset($conversion_data['webp_path']) ? $conversion_data['webp_path'] : 'NOT SET') . "\n";
                echo "        📁 Converted: " . (isset($conversion_data['converted']) && $conversion_data['converted'] ? 'YES' : 'NO') . "\n";
            } else {
                echo "        ❌ Invalid conversion data format\n";
            }
        }
    }
}

// 4. Check files in uploads directory
echo "\n4. Checking files in uploads directory:\n";
$upload_dir = wp_upload_dir();
$current_month_dir = $upload_dir['path'];
echo "   Current upload path: {$current_month_dir}\n";

if (is_dir($current_month_dir)) {
    $files = scandir($current_month_dir);
    $image_files = array_filter($files, function($file) {
        return preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $file);
    });
    
    echo "   ✅ Found " . count($image_files) . " image files in current month\n";
    
    $webp_files = array_filter($image_files, function($file) {
        return preg_match('/\.webp$/i', $file);
    });
    
    echo "   📁 WebP files: " . count($webp_files) . "\n";
    foreach (array_slice($webp_files, 0, 5) as $webp_file) {
        echo "      - {$webp_file}\n";
    }
    
    $original_files = array_filter($image_files, function($file) {
        return preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
    });
    
    echo "   📁 Original format files: " . count($original_files) . "\n";
    foreach (array_slice($original_files, 0, 5) as $original_file) {
        echo "      - {$original_file}\n";
    }
} else {
    echo "   ❌ Upload directory not found\n";
}

echo "\n=== Data Check Complete ===\n";
