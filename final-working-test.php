<?php
/**
 * Final working test with proper settings refresh
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Final Working WebP Test ===\n\n";

// Include required WordPress functions
require_once(ABSPATH . 'wp-admin/includes/image.php');
require_once(ABSPATH . 'wp-admin/includes/file.php');
require_once(ABSPATH . 'wp-admin/includes/media.php');

// 1. Enable auto-convert with proper settings refresh
echo "1. Enabling Auto-Convert with Settings Refresh:\n";

require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php');

// Get current settings
$current_settings = get_option('redco_webp_settings', array());
echo "   Current auto-convert: " . (isset($current_settings['auto_convert_uploads']) && $current_settings['auto_convert_uploads'] ? 'ENABLED' : 'DISABLED') . "\n";

// Force enable auto-convert
$new_settings = array_merge($current_settings, array(
    'auto_convert_uploads' => true,
    'convert_thumbnails' => true,
    'quality' => 85
));

update_option('redco_webp_settings', $new_settings);
echo "   ✅ Auto-convert settings updated in database\n";

// Create a NEW WebP instance AFTER settings update
$webp = new Redco_Smart_WebP_Conversion();

// Verify the new instance has the correct settings
$fresh_settings = $webp->get_settings();
echo "   Fresh instance auto-convert: " . ($fresh_settings['auto_convert_uploads'] ? 'ENABLED' : 'DISABLED') . "\n";

if (!$fresh_settings['auto_convert_uploads']) {
    echo "   ❌ Settings still not updated, creating another fresh instance...\n";

    // Create another fresh instance
    $webp = new Redco_Smart_WebP_Conversion();
    $fresh_settings = $webp->get_settings();
    echo "   ✅ New instance auto-convert: " . ($fresh_settings['auto_convert_uploads'] ? 'ENABLED' : 'DISABLED') . "\n";
}

// 2. Create test image and attachment
echo "\n2. Creating Test Image and Attachment:\n";

$upload_dir = wp_upload_dir();
$test_image_path = $upload_dir['path'] . '/final-working-test.jpg';

$image = imagecreate(250, 180);
$white = imagecolorallocate($image, 255, 255, 255);
$red = imagecolorallocate($image, 200, 50, 50);
imagefill($image, 0, 0, $white);
imagestring($image, 5, 50, 80, 'FINAL TEST', $red);
imagejpeg($image, $test_image_path, 90);
imagedestroy($image);

echo "   ✅ Test image created: " . basename($test_image_path) . "\n";
echo "   📁 Path: " . $test_image_path . "\n";

$attachment_data = array(
    'post_mime_type' => 'image/jpeg',
    'post_title' => 'Final Working Test',
    'post_content' => '',
    'post_status' => 'inherit'
);

$attachment_id = wp_insert_attachment($attachment_data, $test_image_path);
$metadata = wp_generate_attachment_metadata($attachment_id, $test_image_path);
wp_update_attachment_metadata($attachment_id, $metadata);

echo "   ✅ Attachment created with ID: {$attachment_id}\n";

// 3. Manually trigger auto-convert with fresh settings
echo "\n3. Triggering Auto-Convert with Fresh Settings:\n";

echo "   🔄 Calling auto_convert_after_complete_upload() with fresh instance...\n";
$result_metadata = $webp->auto_convert_after_complete_upload($metadata, $attachment_id);

// Check if conversion happened
$conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

if ($conversion_data && isset($conversion_data['converted']) && $conversion_data['converted']) {
    echo "   🎉 AUTO-CONVERSION SUCCESSFUL!\n";
    echo "   📊 Method: " . (isset($conversion_data['method']) ? $conversion_data['method'] : 'unknown') . "\n";
    echo "   📊 Architecture: " . (isset($conversion_data['original_preserved']) && $conversion_data['original_preserved'] ? 'NEW' : 'LEGACY') . "\n";
    
    // 4. Check the stored file_path for corruption
    echo "\n4. Checking Stored File Path:\n";
    
    if (isset($conversion_data['file_path'])) {
        echo "   📁 Stored file_path: " . $conversion_data['file_path'] . "\n";
        echo "   📁 Original test path: " . $test_image_path . "\n";
        echo "   📁 Paths match: " . ($conversion_data['file_path'] === $test_image_path ? '✅ YES' : '❌ NO') . "\n";
        echo "   📁 File exists: " . (file_exists($conversion_data['file_path']) ? '✅ YES' : '❌ NO') . "\n";
        
        // Check for path corruption
        if (strpos($conversion_data['file_path'], 'D:xampp') !== false) {
            echo "   ⚠️ PATH CORRUPTION DETECTED!\n";
            echo "   🔧 This confirms the fix is needed in the conversion process\n";
        } else {
            echo "   ✅ Path correctly formatted - fix is working!\n";
        }
    } else {
        echo "   ❌ file_path not found in conversion data\n";
    }
    
    // 5. Test deletion
    echo "\n5. Testing Deletion:\n";
    
    // Load deletion handlers
    require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/webp-global-handlers.php');
    
    // Count files before deletion
    $files_before = 0;
    $original_files = array();
    $webp_files = array();
    
    // Original main file
    if (isset($conversion_data['file_path']) && file_exists($conversion_data['file_path'])) {
        $files_before++;
        $original_files[] = $conversion_data['file_path'];
    }
    
    // WebP main file
    if (isset($conversion_data['webp_path']) && file_exists($conversion_data['webp_path'])) {
        $files_before++;
        $webp_files[] = $conversion_data['webp_path'];
    }
    
    // Original thumbnails
    foreach ($metadata['sizes'] as $size_name => $size_data) {
        $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
        if (file_exists($thumb_path)) {
            $files_before++;
            $original_files[] = $thumb_path;
        }
    }
    
    // WebP thumbnails
    if (isset($conversion_data['webp_sizes'])) {
        foreach ($conversion_data['webp_sizes'] as $size_name => $webp_size_path) {
            if (file_exists($webp_size_path)) {
                $files_before++;
                $webp_files[] = $webp_size_path;
            }
        }
    }
    
    echo "   📊 Files before deletion: {$files_before}\n";
    echo "   📊 Original files: " . count($original_files) . "\n";
    echo "   📊 WebP files: " . count($webp_files) . "\n";
    
    echo "   🗑️ Deleting attachment {$attachment_id}...\n";
    
    $deleted = wp_delete_attachment($attachment_id, true);
    
    if ($deleted) {
        echo "   ✅ WordPress deletion completed\n";
    } else {
        echo "   ❌ WordPress deletion failed\n";
    }
    
    // 6. Check files after deletion
    echo "\n6. Files After Deletion:\n";
    
    $files_after = 0;
    $original_remaining = 0;
    $webp_remaining = 0;
    
    echo "   Original files:\n";
    foreach ($original_files as $file) {
        $exists = file_exists($file);
        echo "      - " . basename($file) . ": " . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . "\n";
        if ($exists) {
            $files_after++;
            $original_remaining++;
        }
    }
    
    echo "   WebP files:\n";
    foreach ($webp_files as $file) {
        $exists = file_exists($file);
        echo "      - " . basename($file) . ": " . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . "\n";
        if ($exists) {
            $files_after++;
            $webp_remaining++;
        }
    }
    
    echo "   📊 Files after deletion: {$files_after}\n";
    
    // Check metadata cleanup
    $remaining_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
    if ($remaining_data) {
        echo "   📊 Conversion metadata: ❌ STILL EXISTS\n";
    } else {
        echo "   📊 Conversion metadata: ✅ CLEANED UP\n";
    }
    
    // 7. Final result
    echo "\n=== FINAL RESULT ===\n";
    
    if ($files_after === 0 && !$remaining_data) {
        echo "🎉 PERFECT! WebP conversion and deletion working 100%!\n";
        echo "✅ All {$files_before} files deleted successfully\n";
        echo "✅ All conversion metadata cleaned up\n";
        
        if (strpos($conversion_data['file_path'], 'D:xampp') !== false) {
            echo "⚠️ Path corruption was detected but deletion still worked\n";
            echo "🔧 The path reconstruction logic in deletion handlers is working\n";
        } else {
            echo "✅ No path corruption - the fix is working perfectly!\n";
        }
    } else {
        echo "❌ Issues found:\n";
        if ($files_after > 0) {
            echo "   - {$files_after} out of {$files_before} files were not deleted\n";
            if ($original_remaining > 0) {
                echo "   - {$original_remaining} original files not deleted\n";
            }
            if ($webp_remaining > 0) {
                echo "   - {$webp_remaining} WebP files not deleted\n";
            }
        }
        if ($remaining_data) {
            echo "   - Conversion metadata was not cleaned up\n";
        }
    }
    
} else {
    echo "   ❌ Auto-conversion still failed\n";
    echo "   🔧 Check debug logs for specific error messages\n";
}

echo "\n🔧 This test uses the real auto-conversion process with proper settings refresh\n";
