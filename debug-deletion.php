<?php
/**
 * Debug WebP deletion process
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Debugging WebP Deletion Process ===\n\n";

// Check if hooks are properly registered
global $wp_filter;

echo "1. Checking Hook Registration:\n";

$hooks_to_check = [
    'before_delete_post',
    'delete_attachment', 
    'wp_delete_attachment_files'
];

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "✅ {$hook}: " . count($wp_filter[$hook]->callbacks) . " callbacks registered\n";
        
        // Check if our WebP class methods are registered
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'Redco_Smart_WebP_Conversion') {
                    echo "   - WebP method found: {$callback['function'][1]} (priority: {$priority})\n";
                }
            }
        }
    } else {
        echo "❌ {$hook}: No callbacks registered\n";
    }
}

echo "\n2. Checking WebP Instance:\n";

// Check if WebP instance exists
global $redco_webp_instance;
if ($redco_webp_instance) {
    echo "✅ WebP instance exists: " . get_class($redco_webp_instance) . "\n";
} else {
    echo "❌ WebP instance not found\n";
}

echo "\n3. Checking Converted Attachments:\n";

// Get converted attachments
global $wpdb;
$converted_attachments = $wpdb->get_results("
    SELECT p.ID, p.post_title, pm.meta_value as conversion_data
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
    WHERE p.post_type = 'attachment'
    AND pm.meta_key = '_webp_conversion_data'
    AND (pm.meta_value LIKE '%\"converted\":true%' OR pm.meta_value LIKE '%s:9:\"converted\";b:1%')
    LIMIT 3
");

if (empty($converted_attachments)) {
    echo "❌ No converted attachments found\n";
} else {
    echo "✅ Found " . count($converted_attachments) . " converted attachments:\n";
    
    foreach ($converted_attachments as $attachment) {
        $conversion_data = maybe_unserialize($attachment->meta_value);
        $is_new_architecture = isset($conversion_data['original_preserved']) && $conversion_data['original_preserved'];
        
        echo "\n   📄 ID: {$attachment->ID} - {$attachment->post_title}\n";
        echo "      Architecture: " . ($is_new_architecture ? 'NEW' : 'LEGACY') . "\n";
        
        // Check file existence
        $original_file = get_attached_file($attachment->ID);
        $webp_exists = isset($conversion_data['webp_path']) && file_exists($conversion_data['webp_path']);
        $original_exists = $original_file && file_exists($original_file);
        
        echo "      Original exists: " . ($original_exists ? '✅' : '❌') . " ({$original_file})\n";
        echo "      WebP exists: " . ($webp_exists ? '✅' : '❌') . "\n";
        
        if ($webp_exists) {
            echo "      WebP path: " . $conversion_data['webp_path'] . "\n";
        }
    }
}

echo "\n4. Testing Hook Execution:\n";

// Test if we can manually trigger the deletion hooks
if (!empty($converted_attachments)) {
    $test_attachment = $converted_attachments[0];
    $attachment_id = $test_attachment->ID;
    
    echo "Testing with attachment ID: {$attachment_id}\n";
    
    // Load WebP class
    require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php');
    $webp = new Redco_Smart_WebP_Conversion();
    
    // Get post object
    $post = get_post($attachment_id);
    
    if ($post) {
        echo "✅ Post object found\n";
        
        // Test before_delete_attachment method
        echo "Testing before_delete_attachment method...\n";
        $webp->before_delete_attachment($attachment_id, $post);
        
        // Check if files still exist after our cleanup
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
        if ($conversion_data) {
            echo "⚠️ Conversion data still exists after cleanup\n";
        } else {
            echo "✅ Conversion data removed\n";
        }
        
    } else {
        echo "❌ Post object not found\n";
    }
}

echo "\n=== Debug Complete ===\n";
