<?php
/**
 * Enable auto-convert and test real WebP conversion
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Enable Auto-Convert and Test ===\n\n";

// 1. Check current WebP settings
echo "1. Current WebP Settings:\n";

require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php');
$webp = new Redco_Smart_WebP_Conversion();

$current_settings = $webp->get_settings();
echo "   Auto-convert uploads: " . ($current_settings['auto_convert_uploads'] ? 'ENABLED' : 'DISABLED') . "\n";
echo "   Convert thumbnails: " . ($current_settings['convert_thumbnails'] ? 'ENABLED' : 'DISABLED') . "\n";
echo "   Quality: " . $current_settings['quality'] . "\n";

// 2. Enable auto-convert if disabled
if (!$current_settings['auto_convert_uploads']) {
    echo "\n2. Enabling Auto-Convert:\n";
    
    $new_settings = $current_settings;
    $new_settings['auto_convert_uploads'] = true;
    $new_settings['convert_thumbnails'] = true; // Also enable thumbnails for testing
    
    // Save the settings
    update_option('redco_webp_settings', $new_settings);
    
    echo "   ✅ Auto-convert uploads enabled\n";
    echo "   ✅ Convert thumbnails enabled\n";
} else {
    echo "\n2. Auto-convert is already enabled\n";
}

// 3. Test with a new image
echo "\n3. Testing Real WebP Conversion:\n";

// Include required WordPress functions
require_once(ABSPATH . 'wp-admin/includes/image.php');
require_once(ABSPATH . 'wp-admin/includes/file.php');
require_once(ABSPATH . 'wp-admin/includes/media.php');

$upload_dir = wp_upload_dir();
$test_image_path = $upload_dir['path'] . '/auto-convert-test.jpg';

// Create test image
$image = imagecreate(200, 150);
$white = imagecolorallocate($image, 255, 255, 255);
$purple = imagecolorallocate($image, 150, 50, 150);
imagefill($image, 0, 0, $white);
imagestring($image, 5, 50, 70, 'AUTO TEST', $purple);
imagejpeg($image, $test_image_path, 90);
imagedestroy($image);

echo "   ✅ Test image created: " . basename($test_image_path) . "\n";

// Create WordPress attachment
$attachment_data = array(
    'post_mime_type' => 'image/jpeg',
    'post_title' => 'Auto Convert Test',
    'post_content' => '',
    'post_status' => 'inherit'
);

$attachment_id = wp_insert_attachment($attachment_data, $test_image_path);

// Generate metadata - this should trigger auto-conversion
echo "   🔄 Generating metadata (should trigger auto-conversion)...\n";
$metadata = wp_generate_attachment_metadata($attachment_id, $test_image_path);
wp_update_attachment_metadata($attachment_id, $metadata);

echo "   ✅ Attachment created with ID: {$attachment_id}\n";

// Check if conversion happened
$conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

if ($conversion_data && isset($conversion_data['converted']) && $conversion_data['converted']) {
    echo "   🎉 AUTO-CONVERSION SUCCESSFUL!\n";
    echo "   📊 Method: " . (isset($conversion_data['method']) ? $conversion_data['method'] : 'unknown') . "\n";
    echo "   📊 Architecture: " . (isset($conversion_data['original_preserved']) && $conversion_data['original_preserved'] ? 'NEW' : 'LEGACY') . "\n";
    
    // Check the stored file_path
    if (isset($conversion_data['file_path'])) {
        echo "   📁 Stored file_path: " . $conversion_data['file_path'] . "\n";
        echo "   📁 File exists: " . (file_exists($conversion_data['file_path']) ? '✅ YES' : '❌ NO') . "\n";
        
        // Check for path corruption
        if (strpos($conversion_data['file_path'], 'D:xampp') !== false) {
            echo "   ⚠️ PATH CORRUPTION DETECTED!\n";
        } else {
            echo "   ✅ Path appears correctly formatted\n";
        }
    }
    
    // Test deletion immediately
    echo "\n4. Testing Deletion with Real Auto-Converted Data:\n";
    
    // Load deletion handlers
    require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/webp-global-handlers.php');
    
    // Count files before
    $files_before = 0;
    if (isset($conversion_data['file_path']) && file_exists($conversion_data['file_path'])) $files_before++;
    if (isset($conversion_data['webp_path']) && file_exists($conversion_data['webp_path'])) $files_before++;
    foreach ($metadata['sizes'] as $size_data) {
        $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
        if (file_exists($thumb_path)) $files_before++;
    }
    if (isset($conversion_data['webp_sizes'])) {
        foreach ($conversion_data['webp_sizes'] as $webp_size_path) {
            if (file_exists($webp_size_path)) $files_before++;
        }
    }
    
    echo "   📊 Files before deletion: {$files_before}\n";
    echo "   🗑️ Deleting attachment {$attachment_id}...\n";
    
    $deleted = wp_delete_attachment($attachment_id, true);
    
    if ($deleted) {
        echo "   ✅ WordPress deletion completed\n";
    } else {
        echo "   ❌ WordPress deletion failed\n";
    }
    
    // Count files after
    $files_after = 0;
    if (isset($conversion_data['file_path']) && file_exists($conversion_data['file_path'])) $files_after++;
    if (isset($conversion_data['webp_path']) && file_exists($conversion_data['webp_path'])) $files_after++;
    foreach ($metadata['sizes'] as $size_data) {
        $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
        if (file_exists($thumb_path)) $files_after++;
    }
    if (isset($conversion_data['webp_sizes'])) {
        foreach ($conversion_data['webp_sizes'] as $webp_size_path) {
            if (file_exists($webp_size_path)) $files_after++;
        }
    }
    
    echo "   📊 Files after deletion: {$files_after}\n";
    
    // Final result
    echo "\n=== FINAL RESULT ===\n";
    if ($files_after === 0) {
        echo "🎉 PERFECT! Real auto-conversion and deletion working 100%!\n";
        echo "✅ All {$files_before} files deleted successfully\n";
        echo "✅ No path corruption in real conversion process\n";
    } else {
        echo "❌ {$files_after} out of {$files_before} files were not deleted\n";
        echo "🔧 Check debug logs for details\n";
    }
    
} else {
    echo "   ❌ Auto-conversion still failed\n";
    echo "   🔧 Check if WebP module is enabled and auto-convert setting is saved\n";
    
    // Debug: Check if the hook is registered
    global $wp_filter;
    if (isset($wp_filter['wp_generate_attachment_metadata'])) {
        echo "   📊 wp_generate_attachment_metadata hook has " . count($wp_filter['wp_generate_attachment_metadata']->callbacks) . " callbacks\n";
    } else {
        echo "   ❌ wp_generate_attachment_metadata hook not found\n";
    }
}

echo "\n🔧 This test uses the REAL auto-conversion process that happens during upload\n";
