# WebP Deletion Handler Implementation

## Overview

The WebP deletion handler provides comprehensive file cleanup when WebP converted attachments are deleted from the WordPress Media Library. This ensures that both WebP files and original files are properly removed, preventing orphaned files and maintaining a clean file system.

## Problem Solved

### Before Implementation:
- WebP conversions modify WordPress attachment metadata to point to WebP files
- When users delete attachments from Media Library, WordPress only deletes what it thinks are the "main" files (WebP files)
- Original files remain orphaned because WordPress doesn't know about them
- WebP conversion metadata isn't cleaned up
- Results in orphaned files and database clutter

### After Implementation:
- Comprehensive detection of WebP converted attachments during deletion
- Automatic cleanup of both WebP files (main + thumbnails) and original files (main + thumbnails)
- Complete removal of WebP conversion metadata
- Support for both NEW and LEGACY architectures
- Proper error handling and logging

## Architecture Support

### NEW Architecture (Current)
- **Original files**: Preserved but hidden from WordPress
- **WebP files**: Active and displayed in Media Library
- **Deletion behavior**: Both original and WebP files are deleted
- **Metadata**: Conversion metadata is cleaned up

### LEGACY Architecture (Backward Compatibility)
- **Original files**: May or may not exist depending on backup settings
- **WebP files**: Active and displayed in Media Library
- **Deletion behavior**: WebP files are deleted, original files handled if they exist
- **Metadata**: Conversion metadata is cleaned up

## Implementation Details

### Hook Registration
The deletion handler uses multiple WordPress hooks for reliability:

1. **`before_delete_post`** - Early detection and data preservation
2. **`delete_attachment`** - Main deletion processing
3. **`wp_delete_attachment_files`** - File deletion filter integration

### Key Functions

#### `redco_webp_init_deletion_hooks()`
- Registers all deletion hooks when WebP module is enabled
- Called during module initialization

#### `redco_webp_before_delete_attachment($post_id, $post)`
- Detects WebP converted attachments scheduled for deletion
- Stores conversion data in transient for reliable access during deletion
- Provides early warning and data preservation

#### `redco_webp_delete_attachment_handler($post_id)`
- Main deletion processing function
- Retrieves conversion data from transient or metadata
- Calls comprehensive cleanup function

#### `redco_webp_comprehensive_file_cleanup($attachment_id, $conversion_data)`
- Handles actual file deletion for both architectures
- Deletes WebP main file and thumbnails
- Deletes original files (NEW architecture only)
- Cleans up conversion metadata
- Provides detailed logging and error handling

### File Types Handled

#### WebP Files (Always Deleted)
- Main WebP file (`webp_path`)
- WebP thumbnail files (`webp_sizes` array)

#### Original Files (NEW Architecture Only)
- Original main file (`file_path`)
- Original thumbnail files (from `original_metadata['sizes']`)

#### Metadata Cleanup
- Removes `_webp_conversion_data` post meta
- Cleans up transient data

## Error Handling

### Logging
- Comprehensive debug logging when `WP_DEBUG` is enabled
- Detailed file operation results
- Architecture detection logging
- Error and exception logging

### Graceful Degradation
- Continues processing even if some files fail to delete
- Provides detailed cleanup logs for troubleshooting
- Handles missing files gracefully
- Exception handling prevents fatal errors

## Testing

### Automated Testing
Run the test script to verify implementation:
```bash
php test-webp-deletion-handler.php
```

### Manual Testing
1. Convert some images to WebP format
2. Delete a converted attachment from Media Library
3. Verify both WebP and original files are removed
4. Check that conversion metadata is cleaned up
5. Monitor error logs for any issues

## Integration

### Automatic Initialization
The deletion handler is automatically initialized when:
- WebP module is enabled
- WordPress `init` action is triggered
- Global handlers are loaded

### No Configuration Required
The deletion handler works automatically without any configuration. It:
- Detects WebP converted attachments automatically
- Determines architecture type from conversion data
- Handles cleanup based on detected architecture
- Provides comprehensive logging for monitoring

## Benefits

### File System Cleanliness
- No orphaned WebP files
- No orphaned original files
- Complete metadata cleanup
- Prevents disk space waste

### User Experience
- Seamless deletion from Media Library
- No manual cleanup required
- Transparent operation
- Reliable file management

### Developer Experience
- Comprehensive logging for debugging
- Support for both architectures
- Graceful error handling
- Extensible design

## Compatibility

### WordPress Versions
- Compatible with all WordPress versions that support attachment deletion hooks
- Uses standard WordPress APIs and hooks
- No custom database modifications

### Plugin Compatibility
- Works with any media management plugins
- Integrates with WordPress core deletion process
- No conflicts with other file management systems

### Server Compatibility
- Works on all server configurations
- No special PHP extensions required
- Standard file system operations only
