<?php
/**
 * Bypass settings and test WebP conversion directly
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Bypass Settings WebP Test ===\n\n";

// Include required WordPress functions
require_once(ABSPATH . 'wp-admin/includes/image.php');
require_once(ABSPATH . 'wp-admin/includes/file.php');
require_once(ABSPATH . 'wp-admin/includes/media.php');

// 1. Check current settings structure
echo "1. Checking Settings Structure:\n";

require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php');
$webp = new Redco_Smart_WebP_Conversion();

$current_settings = get_option('redco_webp_settings', array());
echo "   Raw settings from database: " . print_r($current_settings, true) . "\n";

$instance_settings = $webp->get_settings();
echo "   Instance settings: " . print_r($instance_settings, true) . "\n";

// 2. Create test image and attachment
echo "\n2. Creating Test Image and Attachment:\n";

$upload_dir = wp_upload_dir();
$test_image_path = $upload_dir['path'] . '/bypass-test.jpg';

$image = imagecreate(200, 150);
$white = imagecolorallocate($image, 255, 255, 255);
$cyan = imagecolorallocate($image, 50, 200, 200);
imagefill($image, 0, 0, $white);
imagestring($image, 5, 50, 70, 'BYPASS', $cyan);
imagejpeg($image, $test_image_path, 90);
imagedestroy($image);

echo "   ✅ Test image created: " . basename($test_image_path) . "\n";

$attachment_data = array(
    'post_mime_type' => 'image/jpeg',
    'post_title' => 'Bypass Settings Test',
    'post_content' => '',
    'post_status' => 'inherit'
);

$attachment_id = wp_insert_attachment($attachment_data, $test_image_path);
$metadata = wp_generate_attachment_metadata($attachment_id, $test_image_path);
wp_update_attachment_metadata($attachment_id, $metadata);

echo "   ✅ Attachment created with ID: {$attachment_id}\n";

// 3. Use direct WebP conversion (bypass auto-convert)
echo "\n3. Using Direct WebP Conversion:\n";

try {
    // Convert main image directly
    echo "   🔄 Converting main image directly...\n";
    $webp_path = $webp->convert_to_webp($test_image_path);
    
    if ($webp_path && file_exists($webp_path)) {
        echo "   ✅ Main image converted: " . basename($webp_path) . "\n";
        
        // Convert thumbnails
        $webp_sizes = array();
        foreach ($metadata['sizes'] as $size_name => $size_data) {
            $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
            if (file_exists($thumb_path)) {
                $webp_thumb_path = $webp->convert_to_webp($thumb_path);
                if ($webp_thumb_path && file_exists($webp_thumb_path)) {
                    $webp_sizes[$size_name] = $webp_thumb_path;
                    echo "   ✅ Thumbnail converted ({$size_name}): " . basename($webp_thumb_path) . "\n";
                }
            }
        }
        
        // Manually create conversion data using the FIXED structure
        echo "\n4. Creating Conversion Data with FIXED Structure:\n";
        
        // CRITICAL: Store the original file path BEFORE any modifications
        $original_file_path = $test_image_path;
        $original_metadata = $metadata;
        
        $conversion_data = array(
            'converted' => true,
            'conversion_date' => current_time('mysql'),
            'conversion_timestamp' => time(),
            'original_size' => filesize($test_image_path),
            'webp_size' => filesize($webp_path),
            'savings' => filesize($test_image_path) - filesize($webp_path),
            'savings_percentage' => round(((filesize($test_image_path) - filesize($webp_path)) / filesize($test_image_path)) * 100, 2),
            'quality' => 85,
            'method' => 'bypass_test',
            'webp_path' => $webp_path,
            'webp_sizes' => $webp_sizes,
            'sizes_converted' => count($webp_sizes),
            'file_path' => $original_file_path, // FIXED: Store original path correctly
            'attachment_title' => 'Bypass Settings Test',
            'original_metadata' => $original_metadata, // Store original metadata for deletion
            'original_preserved' => true // NEW architecture flag
        );
        
        update_post_meta($attachment_id, '_webp_conversion_data', $conversion_data);
        
        echo "   ✅ Conversion data created with FIXED structure\n";
        echo "   📁 Stored file_path: " . $conversion_data['file_path'] . "\n";
        echo "   📁 File exists: " . (file_exists($conversion_data['file_path']) ? '✅ YES' : '❌ NO') . "\n";
        
        // Check for path corruption
        if (strpos($conversion_data['file_path'], 'D:xampp') !== false) {
            echo "   ⚠️ PATH CORRUPTION DETECTED!\n";
        } else {
            echo "   ✅ Path correctly formatted!\n";
        }
        
        // 5. Test deletion
        echo "\n5. Testing Deletion with FIXED Conversion Data:\n";
        
        // Load deletion handlers
        require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/webp-global-handlers.php');
        
        // Count files before deletion
        $files_before = 0;
        $original_files = array();
        $webp_files = array();
        
        // Original main file
        if (file_exists($conversion_data['file_path'])) {
            $files_before++;
            $original_files[] = $conversion_data['file_path'];
        }
        
        // WebP main file
        if (file_exists($conversion_data['webp_path'])) {
            $files_before++;
            $webp_files[] = $conversion_data['webp_path'];
        }
        
        // Original thumbnails
        foreach ($metadata['sizes'] as $size_name => $size_data) {
            $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
            if (file_exists($thumb_path)) {
                $files_before++;
                $original_files[] = $thumb_path;
            }
        }
        
        // WebP thumbnails
        foreach ($webp_sizes as $size_name => $webp_size_path) {
            if (file_exists($webp_size_path)) {
                $files_before++;
                $webp_files[] = $webp_size_path;
            }
        }
        
        echo "   📊 Files before deletion: {$files_before}\n";
        echo "   📊 Original files: " . count($original_files) . "\n";
        echo "   📊 WebP files: " . count($webp_files) . "\n";
        
        echo "   🗑️ Deleting attachment {$attachment_id}...\n";
        
        $deleted = wp_delete_attachment($attachment_id, true);
        
        if ($deleted) {
            echo "   ✅ WordPress deletion completed\n";
        } else {
            echo "   ❌ WordPress deletion failed\n";
        }
        
        // 6. Check files after deletion
        echo "\n6. Files After Deletion:\n";
        
        $files_after = 0;
        $original_remaining = 0;
        $webp_remaining = 0;
        
        echo "   Original files:\n";
        foreach ($original_files as $file) {
            $exists = file_exists($file);
            echo "      - " . basename($file) . ": " . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . "\n";
            if ($exists) {
                $files_after++;
                $original_remaining++;
            }
        }
        
        echo "   WebP files:\n";
        foreach ($webp_files as $file) {
            $exists = file_exists($file);
            echo "      - " . basename($file) . ": " . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . "\n";
            if ($exists) {
                $files_after++;
                $webp_remaining++;
            }
        }
        
        echo "   📊 Files after deletion: {$files_after}\n";
        
        // Check metadata cleanup
        $remaining_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
        if ($remaining_data) {
            echo "   📊 Conversion metadata: ❌ STILL EXISTS\n";
        } else {
            echo "   📊 Conversion metadata: ✅ CLEANED UP\n";
        }
        
        // 7. Final result
        echo "\n=== FINAL RESULT ===\n";
        
        if ($files_after === 0 && !$remaining_data) {
            echo "🎉 PERFECT! WebP conversion and deletion working 100%!\n";
            echo "✅ All {$files_before} files deleted successfully\n";
            echo "✅ All conversion metadata cleaned up\n";
            echo "✅ The fix is working perfectly!\n";
        } else {
            echo "❌ Issues found:\n";
            if ($files_after > 0) {
                echo "   - {$files_after} out of {$files_before} files were not deleted\n";
                if ($original_remaining > 0) {
                    echo "   - {$original_remaining} original files not deleted\n";
                }
                if ($webp_remaining > 0) {
                    echo "   - {$webp_remaining} WebP files not deleted\n";
                }
            }
            if ($remaining_data) {
                echo "   - Conversion metadata was not cleaned up\n";
            }
            
            echo "\n🔧 Check debug logs for detailed information\n";
        }
        
    } else {
        echo "   ❌ Direct WebP conversion failed\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Exception during conversion: " . $e->getMessage() . "\n";
}

echo "\n🔧 This test bypasses auto-convert settings and uses direct conversion\n";
