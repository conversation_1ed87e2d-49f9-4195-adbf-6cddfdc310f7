<?php
/**
 * Accurate WebP Deletion Test - Real-time file checking
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Include required WordPress functions
require_once(ABSPATH . 'wp-admin/includes/image.php');
require_once(ABSPATH . 'wp-admin/includes/file.php');
require_once(ABSPATH . 'wp-admin/includes/media.php');

echo "=== Accurate WebP Deletion Test ===\n\n";

// 1. Create test image
echo "1. Creating Test Image:\n";

$upload_dir = wp_upload_dir();
$test_image_path = $upload_dir['path'] . '/accurate-test.jpg';

$image = imagecreate(200, 150);
$white = imagecolorallocate($image, 255, 255, 255);
$red = imagecolorallocate($image, 200, 50, 50);
imagefill($image, 0, 0, $white);
imagestring($image, 5, 50, 70, 'ACCURATE', $red);
imagejpeg($image, $test_image_path, 90);
imagedestroy($image);

echo "   ✅ Test image created: " . basename($test_image_path) . "\n";
echo "   📁 Path: " . $test_image_path . "\n";

// 2. Create WordPress attachment
echo "\n2. Creating WordPress Attachment:\n";

$attachment_data = array(
    'post_mime_type' => 'image/jpeg',
    'post_title' => 'Accurate Test Image',
    'post_content' => '',
    'post_status' => 'inherit'
);

$attachment_id = wp_insert_attachment($attachment_data, $test_image_path);
$metadata = wp_generate_attachment_metadata($attachment_id, $test_image_path);
wp_update_attachment_metadata($attachment_id, $metadata);

echo "   ✅ Attachment created with ID: {$attachment_id}\n";

// List all files created
$all_files_before = array();
$all_files_before[] = $test_image_path;
foreach ($metadata['sizes'] as $size_name => $size_data) {
    $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
    $all_files_before[] = $thumb_path;
}

echo "   📁 Files created:\n";
foreach ($all_files_before as $file) {
    echo "      - " . basename($file) . " (" . (file_exists($file) ? 'EXISTS' : 'MISSING') . ")\n";
}

// 3. Convert to WebP manually
echo "\n3. Converting to WebP:\n";

require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/class-smart-webp-conversion.php');
$webp = new Redco_Smart_WebP_Conversion();

// Convert main image
$webp_path = $webp->convert_to_webp($test_image_path);
echo "   ✅ Main image converted: " . basename($webp_path) . "\n";

// Convert thumbnails
$webp_sizes = array();
foreach ($metadata['sizes'] as $size_name => $size_data) {
    $thumb_path = dirname($test_image_path) . '/' . $size_data['file'];
    if (file_exists($thumb_path)) {
        $webp_thumb_path = $webp->convert_to_webp($thumb_path);
        if ($webp_thumb_path && file_exists($webp_thumb_path)) {
            $webp_sizes[$size_name] = $webp_thumb_path;
            echo "   ✅ Thumbnail converted ({$size_name}): " . basename($webp_thumb_path) . "\n";
        }
    }
}

// Create conversion data with CORRECT path
$conversion_data = array(
    'converted' => true,
    'conversion_date' => current_time('mysql'),
    'conversion_timestamp' => time(),
    'original_size' => filesize($test_image_path),
    'webp_size' => filesize($webp_path),
    'quality' => 85,
    'method' => 'accurate_test',
    'webp_path' => $webp_path,
    'webp_sizes' => $webp_sizes,
    'file_path' => $test_image_path, // Store CORRECT path
    'attachment_title' => 'Accurate Test Image',
    'original_preserved' => true,
    'original_metadata' => $metadata
);

update_post_meta($attachment_id, '_webp_conversion_data', $conversion_data);

// Update WordPress to point to WebP files
$webp_metadata = $metadata;
$webp_metadata['file'] = str_replace($upload_dir['basedir'] . '/', '', $webp_path);
foreach ($webp_sizes as $size_name => $webp_size_path) {
    if (isset($webp_metadata['sizes'][$size_name])) {
        $webp_metadata['sizes'][$size_name]['file'] = basename($webp_size_path);
    }
}
wp_update_attachment_metadata($attachment_id, $webp_metadata);
update_attached_file($attachment_id, $webp_path);

echo "   ✅ Conversion data saved with CORRECT paths\n";

// 4. Verify all files exist before deletion
echo "\n4. Files Before Deletion:\n";

$files_before_count = 0;
echo "   Original files:\n";
foreach ($all_files_before as $file) {
    $exists = file_exists($file);
    echo "      - " . basename($file) . " (" . ($exists ? 'EXISTS' : 'MISSING') . ")\n";
    if ($exists) $files_before_count++;
}

echo "   WebP files:\n";
$webp_files = array($webp_path);
foreach ($webp_sizes as $size_name => $webp_size_path) {
    $webp_files[] = $webp_size_path;
}

foreach ($webp_files as $file) {
    $exists = file_exists($file);
    echo "      - " . basename($file) . " (" . ($exists ? 'EXISTS' : 'MISSING') . ")\n";
    if ($exists) $files_before_count++;
}

echo "   📊 Total files before deletion: {$files_before_count}\n";

// 5. Test deletion
echo "\n5. Testing Deletion:\n";

// Load deletion handlers
require_once('wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/webp-global-handlers.php');

echo "   🗑️ Deleting attachment {$attachment_id}...\n";

// Delete the attachment
$deleted = wp_delete_attachment($attachment_id, true);

if ($deleted) {
    echo "   ✅ WordPress deletion completed\n";
} else {
    echo "   ❌ WordPress deletion failed\n";
}

// 6. Check files after deletion
echo "\n6. Files After Deletion:\n";

$files_after_count = 0;
echo "   Original files:\n";
foreach ($all_files_before as $file) {
    $exists = file_exists($file);
    echo "      - " . basename($file) . " (" . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . ")\n";
    if ($exists) $files_after_count++;
}

echo "   WebP files:\n";
foreach ($webp_files as $file) {
    $exists = file_exists($file);
    echo "      - " . basename($file) . " (" . ($exists ? '❌ STILL EXISTS' : '✅ DELETED') . ")\n";
    if ($exists) $files_after_count++;
}

echo "   📊 Total files after deletion: {$files_after_count}\n";

// Check metadata cleanup
$remaining_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
if ($remaining_data) {
    echo "   📊 Conversion metadata: ❌ STILL EXISTS\n";
} else {
    echo "   📊 Conversion metadata: ✅ CLEANED UP\n";
}

// 7. Final result
echo "\n=== FINAL RESULT ===\n";

if ($files_after_count === 0 && !$remaining_data) {
    echo "🎉 PERFECT! WebP deletion handler is working 100%!\n";
    echo "✅ All {$files_before_count} files deleted successfully\n";
    echo "✅ All conversion metadata cleaned up\n";
} else {
    echo "❌ Issues found:\n";
    if ($files_after_count > 0) {
        echo "   - {$files_after_count} out of {$files_before_count} files were not deleted\n";
    }
    if ($remaining_data) {
        echo "   - Conversion metadata was not cleaned up\n";
    }
    
    echo "\n🔧 Remaining issues to fix:\n";
    if ($files_after_count > 0) {
        echo "   - Path reconstruction logic needs improvement\n";
        echo "   - Check debug logs for specific file paths that failed\n";
    }
}

echo "\n🔧 Check the debug logs for detailed deletion process information\n";
