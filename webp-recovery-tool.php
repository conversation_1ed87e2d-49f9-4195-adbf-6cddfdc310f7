<?php
/**
 * Redco Optimizer WebP Recovery Tool
 * 
 * This standalone script can be used to recover from WebP conversions
 * if the Redco Optimizer plugin was uninstalled without proper cleanup.
 * 
 * INSTRUCTIONS:
 * 1. Upload this file to your WordPress root directory
 * 2. Access it via: yoursite.com/webp-recovery-tool.php
 * 3. Follow the on-screen instructions
 * 4. Delete this file after recovery is complete
 * 
 * WARNING: This tool makes direct database changes. Always backup your database first!
 */

// Security check - only allow access from WordPress admin users
if (!defined('ABSPATH')) {
    // Load WordPress
    $wp_load_paths = array(
        __DIR__ . '/wp-load.php',
        dirname(__DIR__) . '/wp-load.php',
        dirname(dirname(__DIR__)) . '/wp-load.php'
    );
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $wp_load_path) {
        if (file_exists($wp_load_path)) {
            require_once $wp_load_path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('Error: Could not load WordPress. Please place this file in your WordPress root directory.');
    }
}

// Security check - only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('Access denied. You must be a WordPress administrator to use this tool.');
}

/**
 * WebP Recovery Tool Class
 */
class Redco_WebP_Recovery_Tool {
    
    private $results = array();
    
    /**
     * Run the recovery process
     */
    public function run_recovery() {
        global $wpdb;
        
        $this->results = array(
            'success' => true,
            'images_found' => 0,
            'metadata_restored' => 0,
            'webp_files_found' => 0,
            'webp_files_removed' => 0,
            'database_entries_cleaned' => 0,
            'errors' => array(),
            'warnings' => array()
        );
        
        try {
            // Step 1: Find all WebP conversion metadata
            $converted_attachments = $wpdb->get_results("
                SELECT p.ID, p.post_title, pm.meta_value as conversion_data
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE p.post_type = 'attachment'
                AND pm.meta_key = '_webp_conversion_data'
            ");
            
            $this->results['images_found'] = count($converted_attachments);
            
            if (empty($converted_attachments)) {
                $this->results['warnings'][] = 'No WebP conversion metadata found. Your site may already be clean.';
                return $this->results;
            }
            
            // Step 2: Process each converted attachment
            foreach ($converted_attachments as $attachment) {
                $this->process_attachment($attachment);
            }
            
            // Step 3: Clean up global WebP data
            $this->cleanup_global_webp_data();
            
            // Step 4: Validate recovery
            $this->validate_recovery();
            
        } catch (Exception $e) {
            $this->results['success'] = false;
            $this->results['errors'][] = 'Recovery failed: ' . $e->getMessage();
        }
        
        return $this->results;
    }
    
    /**
     * Process individual attachment recovery
     */
    private function process_attachment($attachment) {
        $attachment_id = $attachment->ID;
        $conversion_data = maybe_unserialize($attachment->conversion_data);
        
        if (!is_array($conversion_data)) {
            $this->results['warnings'][] = "Invalid conversion data for attachment {$attachment_id}";
            return;
        }
        
        // Restore original metadata
        if ($this->restore_attachment_metadata($attachment_id, $conversion_data)) {
            $this->results['metadata_restored']++;
        }
        
        // Count and optionally remove WebP files
        $webp_count = $this->count_webp_files($conversion_data);
        $this->results['webp_files_found'] += $webp_count;
        
        if (isset($_POST['remove_webp_files']) && $_POST['remove_webp_files'] === 'yes') {
            if ($this->remove_webp_files($conversion_data)) {
                $this->results['webp_files_removed'] += $webp_count;
            }
        }
        
        // Clean up conversion metadata
        if (delete_post_meta($attachment_id, '_webp_conversion_data')) {
            $this->results['database_entries_cleaned']++;
        }
    }
    
    /**
     * Restore original attachment metadata
     */
    private function restore_attachment_metadata($attachment_id, $conversion_data) {
        if (!isset($conversion_data['file_path'])) {
            return false;
        }
        
        $original_file_path = $conversion_data['file_path'];
        
        // Check if original file exists
        if (!file_exists($original_file_path)) {
            $this->results['warnings'][] = "Original file missing for attachment {$attachment_id}: {$original_file_path}";
            return false;
        }
        
        $upload_dir = wp_upload_dir();
        $original_relative_path = str_replace($upload_dir['basedir'] . '/', '', $original_file_path);
        
        // Get current metadata
        $metadata = wp_get_attachment_metadata($attachment_id);
        if (!is_array($metadata)) {
            $metadata = array();
        }
        
        // Restore original file path
        $metadata['file'] = $original_relative_path;
        
        // Restore original mime type
        $original_mime_type = wp_check_filetype($original_file_path);
        if ($original_mime_type['type']) {
            wp_update_post(array(
                'ID' => $attachment_id,
                'post_mime_type' => $original_mime_type['type']
            ));
        }
        
        // Restore thumbnail metadata
        if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
            $original_dir = dirname($original_file_path);
            $original_basename = pathinfo($original_file_path, PATHINFO_FILENAME);
            $original_extension = pathinfo($original_file_path, PATHINFO_EXTENSION);
            
            foreach ($metadata['sizes'] as $size_name => $size_data) {
                if (isset($size_data['width']) && isset($size_data['height'])) {
                    $original_thumb_filename = $original_basename . '-' . $size_data['width'] . 'x' . $size_data['height'] . '.' . $original_extension;
                    $original_thumb_path = $original_dir . '/' . $original_thumb_filename;
                    
                    if (file_exists($original_thumb_path)) {
                        $metadata['sizes'][$size_name]['file'] = $original_thumb_filename;
                        $metadata['sizes'][$size_name]['mime-type'] = $original_mime_type['type'];
                    }
                }
            }
        }
        
        return wp_update_attachment_metadata($attachment_id, $metadata) !== false;
    }
    
    /**
     * Count WebP files for an attachment
     */
    private function count_webp_files($conversion_data) {
        $count = 0;
        
        // Count main WebP file
        if (isset($conversion_data['webp_path']) && file_exists($conversion_data['webp_path'])) {
            $count++;
        }
        
        // Count WebP thumbnails
        if (isset($conversion_data['webp_sizes']) && is_array($conversion_data['webp_sizes'])) {
            foreach ($conversion_data['webp_sizes'] as $webp_size_path) {
                if (file_exists($webp_size_path)) {
                    $count++;
                }
            }
        }
        
        return $count;
    }
    
    /**
     * Remove WebP files for an attachment
     */
    private function remove_webp_files($conversion_data) {
        // Safety check - only remove if original exists
        if (!isset($conversion_data['file_path']) || !file_exists($conversion_data['file_path'])) {
            return false;
        }
        
        $removed = false;
        
        // Remove main WebP file
        if (isset($conversion_data['webp_path']) && file_exists($conversion_data['webp_path'])) {
            if (@unlink($conversion_data['webp_path'])) {
                $removed = true;
            }
        }
        
        // Remove WebP thumbnails
        if (isset($conversion_data['webp_sizes']) && is_array($conversion_data['webp_sizes'])) {
            foreach ($conversion_data['webp_sizes'] as $webp_size_path) {
                if (file_exists($webp_size_path)) {
                    @unlink($webp_size_path);
                    $removed = true;
                }
            }
        }
        
        return $removed;
    }
    
    /**
     * Clean up global WebP data
     */
    private function cleanup_global_webp_data() {
        global $wpdb;
        
        // Remove WebP options
        delete_option('redco_optimizer_smart_webp_conversion');
        delete_option('redco_webp_recent_conversions');
        
        // Remove WebP transients
        delete_transient('redco_webp_stats_v2');
        delete_transient('redco_webp_convertible_count');
        
        // Remove any remaining WebP-related options
        $wpdb->query("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE 'redco_webp_%' 
            OR option_name LIKE '_transient_redco_webp_%'
            OR option_name LIKE '_transient_timeout_redco_webp_%'
        ");
    }
    
    /**
     * Validate recovery results
     */
    private function validate_recovery() {
        global $wpdb;
        
        // Check for remaining WebP metadata
        $remaining_metadata = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->postmeta}
            WHERE meta_key = '_webp_conversion_data'
        ");
        
        if ($remaining_metadata > 0) {
            $this->results['warnings'][] = "Found {$remaining_metadata} remaining WebP metadata entries";
        }
        
        // Check for broken attachment references
        $broken_attachments = $wpdb->get_results("
            SELECT p.ID, pm.meta_value
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_wp_attachment_metadata'
            AND pm.meta_value LIKE '%.webp%'
        ");
        
        foreach ($broken_attachments as $attachment) {
            $metadata = maybe_unserialize($attachment->meta_value);
            if (is_array($metadata) && isset($metadata['file'])) {
                $upload_dir = wp_upload_dir();
                $file_path = $upload_dir['basedir'] . '/' . $metadata['file'];
                
                if (!file_exists($file_path)) {
                    $this->results['warnings'][] = "Attachment {$attachment->ID} still references missing WebP file";
                }
            }
        }
    }
}

// Handle form submission
$recovery_results = null;
if (isset($_POST['run_recovery']) && wp_verify_nonce($_POST['recovery_nonce'], 'webp_recovery')) {
    $recovery_tool = new Redco_WebP_Recovery_Tool();
    $recovery_results = $recovery_tool->run_recovery();
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Redco Optimizer WebP Recovery Tool</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .notice { padding: 15px; margin: 20px 0; border-left: 4px solid; }
        .notice-warning { background: #fff3cd; border-color: #ffc107; color: #856404; }
        .notice-success { background: #d4edda; border-color: #28a745; color: #155724; }
        .notice-error { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .button { background: #0073aa; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .button:hover { background: #005a87; }
        .results { background: #f9f9f9; padding: 20px; margin: 20px 0; border: 1px solid #ddd; }
        .warning { color: #856404; }
        .error { color: #721c24; }
        .success { color: #155724; }
    </style>
</head>
<body>
    <h1>🔧 Redco Optimizer WebP Recovery Tool</h1>
    
    <div class="notice notice-warning">
        <strong>⚠️ Important:</strong> This tool will restore your WordPress media library from WebP conversions. 
        Always backup your database before proceeding!
    </div>
    
    <?php if ($recovery_results): ?>
        <div class="results">
            <h2>Recovery Results</h2>
            
            <?php if ($recovery_results['success']): ?>
                <div class="notice notice-success">
                    <strong>✅ Recovery completed successfully!</strong>
                </div>
            <?php else: ?>
                <div class="notice notice-error">
                    <strong>❌ Recovery encountered errors!</strong>
                </div>
            <?php endif; ?>
            
            <h3>Summary:</h3>
            <ul>
                <li><strong>Images Found:</strong> <?php echo $recovery_results['images_found']; ?></li>
                <li><strong>Metadata Restored:</strong> <?php echo $recovery_results['metadata_restored']; ?></li>
                <li><strong>WebP Files Found:</strong> <?php echo $recovery_results['webp_files_found']; ?></li>
                <li><strong>WebP Files Removed:</strong> <?php echo $recovery_results['webp_files_removed']; ?></li>
                <li><strong>Database Entries Cleaned:</strong> <?php echo $recovery_results['database_entries_cleaned']; ?></li>
            </ul>
            
            <?php if (!empty($recovery_results['errors'])): ?>
                <h3 class="error">Errors:</h3>
                <ul>
                    <?php foreach ($recovery_results['errors'] as $error): ?>
                        <li class="error"><?php echo esc_html($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
            
            <?php if (!empty($recovery_results['warnings'])): ?>
                <h3 class="warning">Warnings:</h3>
                <ul>
                    <?php foreach ($recovery_results['warnings'] as $warning): ?>
                        <li class="warning"><?php echo esc_html($warning); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
            
            <?php if ($recovery_results['success'] && empty($recovery_results['errors'])): ?>
                <div class="notice notice-success">
                    <strong>🎉 Your WordPress media library has been successfully restored!</strong><br>
                    You can now safely delete this recovery tool file.
                </div>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <h2>Recovery Options</h2>
        
        <form method="post">
            <?php wp_nonce_field('webp_recovery', 'recovery_nonce'); ?>
            
            <p>
                <label>
                    <input type="checkbox" name="remove_webp_files" value="yes" checked>
                    <strong>Remove WebP files</strong> (recommended - original images will be preserved)
                </label>
            </p>
            
            <p>
                <strong>This tool will:</strong>
            </p>
            <ul>
                <li>✅ Restore original WordPress attachment metadata</li>
                <li>✅ Clean up WebP conversion database entries</li>
                <li>✅ Remove WebP-related options and cache</li>
                <li>✅ Optionally remove WebP files (originals are always preserved)</li>
                <li>✅ Validate the recovery process</li>
            </ul>
            
            <p>
                <button type="submit" name="run_recovery" class="button">
                    🔄 Start Recovery Process
                </button>
            </p>
        </form>
    <?php endif; ?>
    
    <div class="notice notice-warning">
        <strong>Security Notice:</strong> Please delete this file after recovery is complete to prevent unauthorized access.
    </div>
</body>
</html>
