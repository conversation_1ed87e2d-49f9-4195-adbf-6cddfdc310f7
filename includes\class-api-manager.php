<?php
/**
 * API Manager Class
 *
 * @package RedcoOptimizer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * API management functionality
 */
class Redco_API_Manager {
    
    /**
     * Initialize the API manager
     */
    public static function init() {
        // Hook into WordPress
        add_action('rest_api_init', array(__CLASS__, 'register_routes'));
        add_action('wp_ajax_redco_optimizer_ajax', array(__CLASS__, 'handle_ajax'));
        add_action('wp_ajax_nopriv_redco_optimizer_ajax', array(__CLASS__, 'handle_ajax'));
    }
    
    /**
     * Register REST API routes
     */
    public static function register_routes() {
        register_rest_route('redco-optimizer/v1', '/status', array(
            'methods' => 'GET',
            'callback' => array(__CLASS__, 'get_status'),
            'permission_callback' => array(__CLASS__, 'check_permissions'),
        ));
        
        register_rest_route('redco-optimizer/v1', '/optimize', array(
            'methods' => 'POST',
            'callback' => array(__CLASS__, 'run_optimization'),
            'permission_callback' => array(__CLASS__, 'check_permissions'),
        ));
        
        register_rest_route('redco-optimizer/v1', '/settings', array(
            'methods' => array('GET', 'POST'),
            'callback' => array(__CLASS__, 'handle_settings'),
            'permission_callback' => array(__CLASS__, 'check_permissions'),
        ));
    }
    
    /**
     * Check API permissions
     */
    public static function check_permissions() {
        return current_user_can('manage_options');
    }
    
    /**
     * Get plugin status
     */
    public static function get_status($request) {
        $options = get_option('redco_optimizer_options', array());
        
        return rest_ensure_response(array(
            'status' => 'active',
            'version' => REDCO_OPTIMIZER_VERSION,
            'modules_enabled' => isset($options['modules_enabled']) ? $options['modules_enabled'] : array(),
            'last_optimization' => get_option('redco_optimizer_last_optimization', null),
        ));
    }
    
    /**
     * Run optimization
     */
    public static function run_optimization($request) {
        $type = $request->get_param('type');
        
        switch ($type) {
            case 'database':
                if (class_exists('Redco_Database_Optimizer')) {
                    $result = Redco_Database_Optimizer::optimize_tables();
                    return rest_ensure_response(array(
                        'success' => true,
                        'message' => sprintf(__('Optimized %d database tables', 'redco-optimizer'), $result),
                    ));
                }
                break;
                
            case 'cache':
                if (class_exists('Redco_Advanced_Cache')) {
                    Redco_Advanced_Cache::clear_all_cache();
                    return rest_ensure_response(array(
                        'success' => true,
                        'message' => __('Cache cleared successfully', 'redco-optimizer'),
                    ));
                }
                break;
                
            default:
                return new WP_Error('invalid_type', __('Invalid optimization type', 'redco-optimizer'), array('status' => 400));
        }
        
        return new WP_Error('not_available', __('Optimization type not available', 'redco-optimizer'), array('status' => 400));
    }
    
    /**
     * Handle settings
     */
    public static function handle_settings($request) {
        if ($request->get_method() === 'GET') {
            $options = get_option('redco_optimizer_options', array());
            return rest_ensure_response($options);
        } else {
            $settings = $request->get_json_params();
            
            if (is_array($settings)) {
                update_option('redco_optimizer_options', $settings);
                return rest_ensure_response(array(
                    'success' => true,
                    'message' => __('Settings saved successfully', 'redco-optimizer'),
                ));
            }
            
            return new WP_Error('invalid_data', __('Invalid settings data', 'redco-optimizer'), array('status' => 400));
        }
    }
    
    /**
     * Handle AJAX requests
     */
    public static function handle_ajax() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die(__('Security check failed', 'redco-optimizer'));
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }
        
        $action = sanitize_text_field($_POST['action_type']);
        
        switch ($action) {
            case 'get_stats':
                $stats = self::get_performance_stats();
                wp_send_json_success($stats);
                break;
                
            case 'clear_cache':
                if (class_exists('Redco_Advanced_Cache')) {
                    Redco_Advanced_Cache::clear_all_cache();
                    wp_send_json_success(__('Cache cleared successfully', 'redco-optimizer'));
                } else {
                    wp_send_json_error(__('Cache system not available', 'redco-optimizer'));
                }
                break;
                
            default:
                wp_send_json_error(__('Invalid action', 'redco-optimizer'));
        }
    }
    
    /**
     * Get performance statistics
     */
    public static function get_performance_stats() {
        $stats = array(
            'cache_size' => 0,
            'database_size' => 0,
            'optimizations_run' => get_option('redco_optimizer_optimizations_count', 0),
            'last_optimization' => get_option('redco_optimizer_last_optimization', null),
        );
        
        // Get cache size if available
        if (class_exists('Redco_Advanced_Cache')) {
            $stats['cache_size'] = Redco_Advanced_Cache::get_cache_size();
        }
        
        // Get database size if available
        if (class_exists('Redco_Database_Optimizer')) {
            $stats['database_size'] = Redco_Database_Optimizer::get_database_size();
        }
        
        return $stats;
    }
}
