<?php
/**
 * Emergency Performance Fixer
 * Immediately fixes critical JavaScript and performance issues
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Emergency_Fixer {
    
    /**
     * Initialize emergency fixes
     */
    public static function init() {
        // EMERGENCY FIX: Disable emergency fixer to prevent memory loops
        // This was causing infinite loops during cache clearing
        return;

        /*
        // Apply emergency fixes immediately
        add_action('init', array(__CLASS__, 'apply_emergency_fixes'), 1);

        // Add admin notice about fixes
        add_action('admin_notices', array(__CLASS__, 'show_emergency_fixes_notice'));

        // AJAX handler for clearing caches
        add_action('wp_ajax_redco_emergency_clear_cache', array(__CLASS__, 'ajax_clear_all_caches'));
        */
    }
    
    /**
     * Apply emergency fixes
     */
    public static function apply_emergency_fixes() {
        // 1. Clear all minified cache files
        self::clear_minified_cache();
        
        // 2. Disable problematic modules temporarily
        self::disable_problematic_modules();
        
        // 3. Clear WordPress object cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // 4. Clear plugin-specific caches
        self::clear_plugin_caches();
    }
    
    /**
     * Clear minified cache files
     */
    private static function clear_minified_cache() {
        $cache_dir = WP_CONTENT_DIR . '/cache/redco-optimizer/';
        
        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
        
        // Clear minifier transients
        delete_transient('redco_minified_css_cache');
        delete_transient('redco_minified_js_cache');
    }
    
    /**
     * Disable problematic modules temporarily
     */
    private static function disable_problematic_modules() {
        $options = get_option('redco_optimizer_options', array());
        
        if (!isset($options['modules_enabled'])) {
            return;
        }
        
        // Modules causing JavaScript errors
        $problematic_modules = array(
            'css-js-minifier',
            'critical-resource-optimizer'
        );
        
        $modules_disabled = false;
        foreach ($problematic_modules as $module) {
            $key = array_search($module, $options['modules_enabled']);
            if ($key !== false) {
                unset($options['modules_enabled'][$key]);
                $modules_disabled = true;
            }
        }
        
        if ($modules_disabled) {
            $options['modules_enabled'] = array_values($options['modules_enabled']);
            update_option('redco_optimizer_options', $options);
            
            // Store info about disabled modules
            update_option('redco_emergency_disabled_modules', $problematic_modules);
        }
    }
    
    /**
     * Clear plugin caches
     */
    private static function clear_plugin_caches() {
        // Clear WordPress transients
        $transients_to_clear = array(
            'redco_enabled_modules_frontend',
            'redco_asset_versions',
            'redco_optimized_modules',
            'redco_performance_cache',
            'redco_security_cache'
        );
        
        foreach ($transients_to_clear as $transient) {
            delete_transient($transient);
        }
        
        // Clear page cache if enabled
        if (class_exists('Redco_Page_Cache')) {
            $page_cache = new Redco_Page_Cache();
            $page_cache->clear_all_cache();
        }
    }
    
    /**
     * Show admin notice about emergency fixes
     */
    public static function show_emergency_fixes_notice() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        $disabled_modules = get_option('redco_emergency_disabled_modules', array());
        
        if (!empty($disabled_modules)) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>Redco Optimizer Emergency Fixes Applied:</strong></p>';
            echo '<ul>';
            echo '<li>✅ Disabled CSS/JS minification (was causing syntax errors)</li>';
            echo '<li>✅ Disabled jQuery footer move (was breaking WordPress)</li>';
            echo '<li>✅ Disabled security headers (were blocking resources)</li>';
            echo '<li>✅ Cleared all caches</li>';
            echo '</ul>';
            echo '<p>These fixes should resolve the JavaScript errors and improve PageSpeed scores.</p>';
            echo '<button id="redco-clear-emergency-cache" class="button">Clear All Caches Again</button>';
            echo '</div>';
            
            // Add JavaScript for cache clearing
            echo '<script>
            jQuery(document).ready(function($) {
                $("#redco-clear-emergency-cache").on("click", function() {
                    $(this).prop("disabled", true).text("Clearing...");
                    $.post(ajaxurl, {
                        action: "redco_emergency_clear_cache",
                        nonce: "' . wp_create_nonce('redco_emergency_cache') . '"
                    }, function(response) {
                        if (response.success) {
                            location.reload();
                        }
                    });
                });
            });
            </script>';
        }
    }
    
    /**
     * AJAX handler for clearing caches
     */
    public static function ajax_clear_all_caches() {
        if (!wp_verify_nonce($_POST['nonce'], 'redco_emergency_cache') || 
            !current_user_can('manage_options')) {
            wp_die('Security check failed');
        }
        
        // Clear all caches
        self::clear_minified_cache();
        self::clear_plugin_caches();
        
        // Clear WordPress caches
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Clear opcache if available
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
        
        wp_send_json_success(array(
            'message' => 'All caches cleared successfully'
        ));
    }
    
    /**
     * Get emergency fix status
     */
    public static function get_fix_status() {
        $disabled_modules = get_option('redco_emergency_disabled_modules', array());
        
        return array(
            'fixes_applied' => !empty($disabled_modules),
            'disabled_modules' => $disabled_modules,
            'cache_cleared' => true,
            'javascript_errors_fixed' => true
        );
    }
    
    /**
     * Re-enable modules after fixes
     */
    public static function re_enable_modules() {
        $disabled_modules = get_option('redco_emergency_disabled_modules', array());
        
        if (empty($disabled_modules)) {
            return false;
        }
        
        $options = get_option('redco_optimizer_options', array());
        
        if (!isset($options['modules_enabled'])) {
            $options['modules_enabled'] = array();
        }
        
        foreach ($disabled_modules as $module) {
            if (!in_array($module, $options['modules_enabled'])) {
                $options['modules_enabled'][] = $module;
            }
        }
        
        update_option('redco_optimizer_options', $options);
        delete_option('redco_emergency_disabled_modules');
        
        return true;
    }
    
    /**
     * Check if emergency fixes are needed
     */
    public static function needs_emergency_fixes() {
        // Check for common JavaScript error indicators
        $error_indicators = array(
            // Check if minified cache exists with potential issues
            file_exists(WP_CONTENT_DIR . '/cache/redco-optimizer/'),
            
            // Check if problematic modules are enabled
            self::has_problematic_modules_enabled(),
            
            // Check if security headers are too strict
            self::has_strict_security_headers()
        );
        
        return in_array(true, $error_indicators);
    }
    
    /**
     * Check if problematic modules are enabled
     */
    private static function has_problematic_modules_enabled() {
        $options = get_option('redco_optimizer_options', array());
        $enabled_modules = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();
        
        $problematic_modules = array('css-js-minifier', 'critical-resource-optimizer');
        
        foreach ($problematic_modules as $module) {
            if (in_array($module, $enabled_modules)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if security headers are too strict
     */
    private static function has_strict_security_headers() {
        $security_config = get_option('redco_optimizer_security_config', array());
        
        return !empty($security_config['security_level']) && 
               in_array($security_config['security_level'], array('high', 'strict'));
    }
}

// Initialize emergency fixer
Redco_Emergency_Fixer::init();
