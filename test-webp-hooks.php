<?php
/**
 * Test WebP deletion hooks registration
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== Testing WebP Deletion Hooks ===\n\n";

// 1. Check if WebP module is enabled
$enabled_modules = get_option('redco_optimizer_enabled_modules', array());
$webp_enabled = in_array('smart-webp-conversion', $enabled_modules);

echo "1. Module Status:\n";
echo "   Enabled modules: " . implode(', ', $enabled_modules) . "\n";
echo "   WebP enabled: " . ($webp_enabled ? '✅ YES' : '❌ NO') . "\n\n";

if (!$webp_enabled) {
    echo "❌ WebP module is not enabled. Enabling it now...\n";
    
    // Enable the WebP module
    if (!in_array('smart-webp-conversion', $enabled_modules)) {
        $enabled_modules[] = 'smart-webp-conversion';
        update_option('redco_optimizer_enabled_modules', $enabled_modules);
        echo "✅ WebP module enabled successfully\n\n";
    }
}

// 2. Check if global hooks are registered
global $wp_filter;

echo "2. Hook Registration Check:\n";

$hooks_to_check = [
    'wp_delete_post',
    'deleted_post',
    'before_delete_post',
    'delete_attachment',
    'wp_delete_attachment_files'
];

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "   ✅ {$hook}: " . count($wp_filter[$hook]->callbacks) . " callbacks\n";
        
        // Check for WebP global callbacks
        $webp_found = false;
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function'])) {
                    $function_name = '';
                    if (is_string($callback['function'][1])) {
                        $function_name = $callback['function'][1];
                    }
                } elseif (is_string($callback['function'])) {
                    $function_name = $callback['function'];
                }
                
                if (strpos($function_name, 'redco_webp_') === 0) {
                    echo "      - WebP Global: {$function_name} (priority: {$priority})\n";
                    $webp_found = true;
                }
            }
        }
        
        if (!$webp_found) {
            echo "      ⚠️ No WebP global callbacks found\n";
        }
    } else {
        echo "   ❌ {$hook}: No callbacks registered\n";
    }
}

echo "\n3. Function Existence Check:\n";

$webp_functions = [
    'redco_webp_global_deletion_hooks',
    'redco_webp_handle_post_deletion',
    'redco_webp_handle_post_deleted',
    'redco_webp_cleanup_files_comprehensive'
];

foreach ($webp_functions as $function) {
    if (function_exists($function)) {
        echo "   ✅ {$function}: EXISTS\n";
    } else {
        echo "   ❌ {$function}: NOT FOUND\n";
    }
}

echo "\n4. Manual Hook Registration Test:\n";

// Try to manually call the hook registration function
if (function_exists('redco_webp_global_deletion_hooks')) {
    echo "   Calling redco_webp_global_deletion_hooks() manually...\n";
    redco_webp_global_deletion_hooks();
    
    // Check if hooks are now registered
    if (isset($wp_filter['wp_delete_post'])) {
        $webp_found = false;
        foreach ($wp_filter['wp_delete_post']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_string($callback['function']) && strpos($callback['function'], 'redco_webp_') === 0) {
                    echo "   ✅ WebP hooks now registered after manual call\n";
                    $webp_found = true;
                    break 2;
                }
            }
        }
        
        if (!$webp_found) {
            echo "   ❌ WebP hooks still not found after manual call\n";
        }
    }
} else {
    echo "   ❌ redco_webp_global_deletion_hooks function not found\n";
}

echo "\n5. Test Deletion Hook Execution:\n";

// Test if we can manually trigger a deletion hook
if (function_exists('redco_webp_handle_post_deletion')) {
    echo "   Testing redco_webp_handle_post_deletion function...\n";
    
    // Create a fake post object for testing
    $fake_post = new stdClass();
    $fake_post->post_type = 'attachment';
    $fake_post->ID = 999999; // Non-existent ID
    
    // Call the function
    redco_webp_handle_post_deletion(999999, $fake_post);
    echo "   ✅ Function executed without errors\n";
} else {
    echo "   ❌ redco_webp_handle_post_deletion function not found\n";
}

echo "\n=== Test Complete ===\n";
